VMware-vCD-TenantReport PowerShell Module
=============

# About

## Project Owner:

<PERSON> [@vMarkus_K](https://twitter.com/vMarkus_K)

MY CLOUD-(R)EVOLUTION [mycloudrevolution.com](http://mycloudrevolution.com/)

## Project WebSite:

[mycloudrevolution.com](http://mycloudrevolution.com/)

## Project Documentation:

[Read the Docs](http://readthedocs.io/)

## Project Description:

The 'VMware-vCD-TenantReport' PowerShell Module creates with the Fuction 'Get-VcdTenantReport' a HTML Report of your vCloud Director Objects.

![Get-VcdTenantReport](/media/Get-VcdTenantReport.png)

Big thanks to [<PERSON>](https://twitter.com/tdewin) for his great [PowerStartHTML](https://github.com/tdewin/randomsamples/tree/master/powerstarthtml) PowerShell Module which is used to generate the Report for this Module.




