﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.Xml;

namespace VMware.vSphere.SsoAdminClient.SsoAdminServiceReference2
{

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoFaultInvalidCredentials : SecurityError
{
}

/// <remarks/>
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultNotAuthenticated))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultNoPermission))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultInvalidCredentials))]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class SecurityError : RuntimeFault
{
}

/// <remarks/>
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultRuntimeServiceFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultNoDomainSearchPermission))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultInternalFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultNativeADRegistrationFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultLocalOSDomainRegistrationFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultInvalidProviderFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultHostNotJoinedRequiredDomainFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultDomainManagerFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(UnexpectedFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SystemError))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SecurityError))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultNotAuthenticated))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultNoPermission))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultInvalidCredentials))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(RequestCanceled))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(NotSupported))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(NotImplemented))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(NotEnoughLicenses))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(ManagedObjectNotFound))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidRequest))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(MethodNotFound))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidArgument))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(HostCommunication))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(HostNotReachable))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(HostNotConnected))]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class RuntimeFault : MethodFault
{
}

/// <remarks/>
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultServiceFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultInvalidPrincipalFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultSmtpConfigNotSetFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultPasswordPolicyViolationFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultNoSuchRelyingPartyFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultNoSuchExternalSTSConfigFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultNoSuchConfigFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultInvalidPasswordPolicyFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultGroupCyclicDependencyFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultExtraneousCertsInCertChainFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultExternalSTSExtraneousCertsInCertChainFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultExternalSTSCertChainInvalidTrustedPathFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultDuplicateSolutionCertificateFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultDuplicateDomainNameFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultDuplicateDataFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultDomainNotFoundFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultDirectoryServiceConnectionFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultCertificateDeletionFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultCertChainInvalidTrustedPathFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultADIDSAlreadyExistFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultADDomainUnknownDomainFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultADDomainNotJoinedFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultADDomainAlreadyJoinedFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultADDomainAccessDeniedFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(RuntimeFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultRuntimeServiceFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultNoDomainSearchPermission))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultInternalFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultNativeADRegistrationFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultLocalOSDomainRegistrationFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultInvalidProviderFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultHostNotJoinedRequiredDomainFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultDomainManagerFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(UnexpectedFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SystemError))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SecurityError))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultNotAuthenticated))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultNoPermission))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultInvalidCredentials))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(RequestCanceled))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(NotSupported))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(NotImplemented))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(NotEnoughLicenses))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(ManagedObjectNotFound))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidRequest))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(MethodNotFound))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidArgument))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(HostCommunication))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(HostNotReachable))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(HostNotConnected))]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class MethodFault
{
}

/// <remarks/>
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoGroupcheckServiceContent))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminVmHost))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminUser))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminTrustedSTSConfig))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminSsoHealthStats))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminSmtpConfig))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminServiceContent))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminPrincipalDiscoveryServiceSearchCriteria))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminSolutionDetails))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminSolutionUser))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminPrincipalDiscoveryServiceSearchResult))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminPersonUser))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminPersonDetails))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminPasswordPolicy))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminPasswordFormatAlphabeticRestriction))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminPasswordFormatLengthRestriction))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminPasswordFormat))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminPasswordExpirationConfig))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminMailContent))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminLockoutPolicy))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminIdentitySources))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminIdentitySourceManagementServiceAuthenticationCredentials))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminLdapIdentitySourceAuthenticationDetails))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminLdapIdentitySourceDetails))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminIdentitySource))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminLdapIdentitySource))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminServiceEndpoint))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminIDPConfiguration))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminGroupDetails))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminGroup))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminExternalDomainAuthenticationDetails))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminExternalDomainAttributeMapping))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminExternalDomainObjectMapping))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminExternalDomainSchemaDetails))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminExternalDomainDetails))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminExternalDomain))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminDomains))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminDomainManagementServiceAuthenticationCredentails))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminDomain))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminConfigurationManagementServiceTokenClaimGroupMapping))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminConfigurationManagementServiceTokenClaimAttribute))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminConfigurationManagementServiceAttributeConfig))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminConfigurationManagementServiceCertificateChain))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminClientCertPolicy))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminAuthnPolicy))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminAuthenticationAccountInfo))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminActiveDirectoryJoinInfo))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoPrincipalId))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAboutInfo))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(LocalizedMethodFault))]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class DynamicData
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoGroupcheckServiceContent : DynamicData
{

    private SsoAboutInfo aboutInfoField;

    private ManagedObjectReference sessionManagerField;

    private ManagedObjectReference groupCheckServiceField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public SsoAboutInfo aboutInfo
    {
        get
        {
            return this.aboutInfoField;
        }
        set
        {
            this.aboutInfoField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public ManagedObjectReference sessionManager
    {
        get
        {
            return this.sessionManagerField;
        }
        set
        {
            this.sessionManagerField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public ManagedObjectReference groupCheckService
    {
        get
        {
            return this.groupCheckServiceField;
        }
        set
        {
            this.groupCheckServiceField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAboutInfo : DynamicData
{

    private string versionField;

    private string buildField;

    private string apiRevisionField;

    private string clusterIdField;

    private string deploymentIdField;

    private string ssoProductInfoField;

    private string ssoProductVersionMajorField;

    private string ssoProductVersionMinorField;

    private string ssoProductVersionMaintField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string version
    {
        get
        {
            return this.versionField;
        }
        set
        {
            this.versionField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string build
    {
        get
        {
            return this.buildField;
        }
        set
        {
            this.buildField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public string apiRevision
    {
        get
        {
            return this.apiRevisionField;
        }
        set
        {
            this.apiRevisionField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public string clusterId
    {
        get
        {
            return this.clusterIdField;
        }
        set
        {
            this.clusterIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public string deploymentId
    {
        get
        {
            return this.deploymentIdField;
        }
        set
        {
            this.deploymentIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public string ssoProductInfo
    {
        get
        {
            return this.ssoProductInfoField;
        }
        set
        {
            this.ssoProductInfoField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=6)]
    public string ssoProductVersionMajor
    {
        get
        {
            return this.ssoProductVersionMajorField;
        }
        set
        {
            this.ssoProductVersionMajorField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public string ssoProductVersionMinor
    {
        get
        {
            return this.ssoProductVersionMinorField;
        }
        set
        {
            this.ssoProductVersionMinorField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=8)]
    public string ssoProductVersionMaint
    {
        get
        {
            return this.ssoProductVersionMaintField;
        }
        set
        {
            this.ssoProductVersionMaintField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class ManagedObjectReference
{

    private string typeField;

    private string valueField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public string type
    {
        get
        {
            return this.typeField;
        }
        set
        {
            this.typeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlTextAttribute()]
    public string Value
    {
        get
        {
            return this.valueField;
        }
        set
        {
            this.valueField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminVmHost : DynamicData
{

    private string hostNameField;

    private bool domainControllerField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string hostName
    {
        get
        {
            return this.hostNameField;
        }
        set
        {
            this.hostNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public bool domainController
    {
        get
        {
            return this.domainControllerField;
        }
        set
        {
            this.domainControllerField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminUser : DynamicData
{

    private SsoPrincipalId idField;

    private SsoPrincipalId aliasField;

    private string kindField;

    private string descriptionField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public SsoPrincipalId id
    {
        get
        {
            return this.idField;
        }
        set
        {
            this.idField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public SsoPrincipalId alias
    {
        get
        {
            return this.aliasField;
        }
        set
        {
            this.aliasField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public string kind
    {
        get
        {
            return this.kindField;
        }
        set
        {
            this.kindField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoPrincipalId : DynamicData
{

    private string nameField;

    private string domainField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string name
    {
        get
        {
            return this.nameField;
        }
        set
        {
            this.nameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string domain
    {
        get
        {
            return this.domainField;
        }
        set
        {
            this.domainField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminTrustedSTSConfig : DynamicData
{

    private string issuerField;

    private SsoAdminConfigurationManagementServiceCertificateChain signingCertChainField;

    private SsoAdminConfigurationManagementServiceAttributeConfig[] subjectFormatMappingsField;

    private SsoAdminConfigurationManagementServiceTokenClaimGroupMapping[] tokenClaimGroupMappingsField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string issuer
    {
        get
        {
            return this.issuerField;
        }
        set
        {
            this.issuerField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public SsoAdminConfigurationManagementServiceCertificateChain signingCertChain
    {
        get
        {
            return this.signingCertChainField;
        }
        set
        {
            this.signingCertChainField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("subjectFormatMappings", Order=2)]
    public SsoAdminConfigurationManagementServiceAttributeConfig[] subjectFormatMappings
    {
        get
        {
            return this.subjectFormatMappingsField;
        }
        set
        {
            this.subjectFormatMappingsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("tokenClaimGroupMappings", Order=3)]
    public SsoAdminConfigurationManagementServiceTokenClaimGroupMapping[] tokenClaimGroupMappings
    {
        get
        {
            return this.tokenClaimGroupMappingsField;
        }
        set
        {
            this.tokenClaimGroupMappingsField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminConfigurationManagementServiceCertificateChain : DynamicData
{

    private string[] certificatesField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("certificates", Order=0)]
    public string[] certificates
    {
        get
        {
            return this.certificatesField;
        }
        set
        {
            this.certificatesField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminConfigurationManagementServiceAttributeConfig : DynamicData
{

    private string tokenAttributeField;

    private string storeAttributeField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string tokenAttribute
    {
        get
        {
            return this.tokenAttributeField;
        }
        set
        {
            this.tokenAttributeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string storeAttribute
    {
        get
        {
            return this.storeAttributeField;
        }
        set
        {
            this.storeAttributeField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminConfigurationManagementServiceTokenClaimGroupMapping : DynamicData
{

    private SsoAdminConfigurationManagementServiceTokenClaimAttribute tokenClaimField;

    private string[] groupsField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public SsoAdminConfigurationManagementServiceTokenClaimAttribute tokenClaim
    {
        get
        {
            return this.tokenClaimField;
        }
        set
        {
            this.tokenClaimField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("groups", Order=1)]
    public string[] groups
    {
        get
        {
            return this.groupsField;
        }
        set
        {
            this.groupsField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminConfigurationManagementServiceTokenClaimAttribute : DynamicData
{

    private string claimNameField;

    private string claimValueField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string claimName
    {
        get
        {
            return this.claimNameField;
        }
        set
        {
            this.claimNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string claimValue
    {
        get
        {
            return this.claimValueField;
        }
        set
        {
            this.claimValueField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminSsoHealthStats : DynamicData
{

    private string tenantField;

    private int totalTokensGeneratedField;

    private int totalTokensRenewedField;

    private int generatedTokensForTenantField;

    private int renewedTokensForTenantField;

    private long uptimeIDMField;

    private long uptimeSTSField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string tenant
    {
        get
        {
            return this.tenantField;
        }
        set
        {
            this.tenantField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int totalTokensGenerated
    {
        get
        {
            return this.totalTokensGeneratedField;
        }
        set
        {
            this.totalTokensGeneratedField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int totalTokensRenewed
    {
        get
        {
            return this.totalTokensRenewedField;
        }
        set
        {
            this.totalTokensRenewedField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int generatedTokensForTenant
    {
        get
        {
            return this.generatedTokensForTenantField;
        }
        set
        {
            this.generatedTokensForTenantField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int renewedTokensForTenant
    {
        get
        {
            return this.renewedTokensForTenantField;
        }
        set
        {
            this.renewedTokensForTenantField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public long uptimeIDM
    {
        get
        {
            return this.uptimeIDMField;
        }
        set
        {
            this.uptimeIDMField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=6)]
    public long uptimeSTS
    {
        get
        {
            return this.uptimeSTSField;
        }
        set
        {
            this.uptimeSTSField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminSmtpConfig : DynamicData
{

    private string hostField;

    private int portField;

    private bool portFieldSpecified;

    private bool authenticateField;

    private bool authenticateFieldSpecified;

    private string userField;

    private string passwordField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string host
    {
        get
        {
            return this.hostField;
        }
        set
        {
            this.hostField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int port
    {
        get
        {
            return this.portField;
        }
        set
        {
            this.portField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool portSpecified
    {
        get
        {
            return this.portFieldSpecified;
        }
        set
        {
            this.portFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public bool authenticate
    {
        get
        {
            return this.authenticateField;
        }
        set
        {
            this.authenticateField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool authenticateSpecified
    {
        get
        {
            return this.authenticateFieldSpecified;
        }
        set
        {
            this.authenticateFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public string user
    {
        get
        {
            return this.userField;
        }
        set
        {
            this.userField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public string password
    {
        get
        {
            return this.passwordField;
        }
        set
        {
            this.passwordField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminServiceContent : DynamicData
{

    private SsoAboutInfo aboutInfoField;

    private ManagedObjectReference sessionManagerField;

    private ManagedObjectReference configurationManagementServiceField;

    private ManagedObjectReference smtpManagementServiceField;

    private ManagedObjectReference principalDiscoveryServiceField;

    private ManagedObjectReference principalManagementServiceField;

    private ManagedObjectReference roleManagementServiceField;

    private ManagedObjectReference passwordPolicyServiceField;

    private ManagedObjectReference lockoutPolicyServiceField;

    private ManagedObjectReference domainManagementServiceField;

    private ManagedObjectReference identitySourceManagementServiceField;

    private ManagedObjectReference systemManagementServiceField;

    private ManagedObjectReference computerManagementServiceField;

    private ManagedObjectReference ssoHealthManagementServiceField;

    private ManagedObjectReference deploymentInformationServiceField;

    private ManagedObjectReference replicationServiceField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public SsoAboutInfo aboutInfo
    {
        get
        {
            return this.aboutInfoField;
        }
        set
        {
            this.aboutInfoField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public ManagedObjectReference sessionManager
    {
        get
        {
            return this.sessionManagerField;
        }
        set
        {
            this.sessionManagerField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public ManagedObjectReference configurationManagementService
    {
        get
        {
            return this.configurationManagementServiceField;
        }
        set
        {
            this.configurationManagementServiceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public ManagedObjectReference smtpManagementService
    {
        get
        {
            return this.smtpManagementServiceField;
        }
        set
        {
            this.smtpManagementServiceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public ManagedObjectReference principalDiscoveryService
    {
        get
        {
            return this.principalDiscoveryServiceField;
        }
        set
        {
            this.principalDiscoveryServiceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public ManagedObjectReference principalManagementService
    {
        get
        {
            return this.principalManagementServiceField;
        }
        set
        {
            this.principalManagementServiceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=6)]
    public ManagedObjectReference roleManagementService
    {
        get
        {
            return this.roleManagementServiceField;
        }
        set
        {
            this.roleManagementServiceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public ManagedObjectReference passwordPolicyService
    {
        get
        {
            return this.passwordPolicyServiceField;
        }
        set
        {
            this.passwordPolicyServiceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=8)]
    public ManagedObjectReference lockoutPolicyService
    {
        get
        {
            return this.lockoutPolicyServiceField;
        }
        set
        {
            this.lockoutPolicyServiceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=9)]
    public ManagedObjectReference domainManagementService
    {
        get
        {
            return this.domainManagementServiceField;
        }
        set
        {
            this.domainManagementServiceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=10)]
    public ManagedObjectReference identitySourceManagementService
    {
        get
        {
            return this.identitySourceManagementServiceField;
        }
        set
        {
            this.identitySourceManagementServiceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=11)]
    public ManagedObjectReference systemManagementService
    {
        get
        {
            return this.systemManagementServiceField;
        }
        set
        {
            this.systemManagementServiceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=12)]
    public ManagedObjectReference computerManagementService
    {
        get
        {
            return this.computerManagementServiceField;
        }
        set
        {
            this.computerManagementServiceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=13)]
    public ManagedObjectReference ssoHealthManagementService
    {
        get
        {
            return this.ssoHealthManagementServiceField;
        }
        set
        {
            this.ssoHealthManagementServiceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=14)]
    public ManagedObjectReference deploymentInformationService
    {
        get
        {
            return this.deploymentInformationServiceField;
        }
        set
        {
            this.deploymentInformationServiceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=15)]
    public ManagedObjectReference replicationService
    {
        get
        {
            return this.replicationServiceField;
        }
        set
        {
            this.replicationServiceField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminPrincipalDiscoveryServiceSearchCriteria : DynamicData
{

    private string searchStringField;

    private string domainField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string searchString
    {
        get
        {
            return this.searchStringField;
        }
        set
        {
            this.searchStringField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string domain
    {
        get
        {
            return this.domainField;
        }
        set
        {
            this.domainField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminSolutionDetails : DynamicData
{

    private string descriptionField;

    private string certificateField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string certificate
    {
        get
        {
            return this.certificateField;
        }
        set
        {
            this.certificateField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminSolutionUser : DynamicData
{

    private SsoPrincipalId idField;

    private SsoPrincipalId aliasField;

    private SsoAdminSolutionDetails detailsField;

    private bool disabledField;

    private bool externalField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public SsoPrincipalId id
    {
        get
        {
            return this.idField;
        }
        set
        {
            this.idField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public SsoPrincipalId alias
    {
        get
        {
            return this.aliasField;
        }
        set
        {
            this.aliasField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public SsoAdminSolutionDetails details
    {
        get
        {
            return this.detailsField;
        }
        set
        {
            this.detailsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public bool disabled
    {
        get
        {
            return this.disabledField;
        }
        set
        {
            this.disabledField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public bool external
    {
        get
        {
            return this.externalField;
        }
        set
        {
            this.externalField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminPrincipalDiscoveryServiceSearchResult : DynamicData
{

    private SsoAdminPersonUser[] personUsersField;

    private SsoAdminSolutionUser[] solutionUsersField;

    private SsoAdminGroup[] groupsField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("personUsers", Order=0)]
    public SsoAdminPersonUser[] personUsers
    {
        get
        {
            return this.personUsersField;
        }
        set
        {
            this.personUsersField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("solutionUsers", Order=1)]
    public SsoAdminSolutionUser[] solutionUsers
    {
        get
        {
            return this.solutionUsersField;
        }
        set
        {
            this.solutionUsersField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("groups", Order=2)]
    public SsoAdminGroup[] groups
    {
        get
        {
            return this.groupsField;
        }
        set
        {
            this.groupsField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminPersonUser : DynamicData
{

    private SsoPrincipalId idField;

    private SsoPrincipalId aliasField;

    private SsoAdminPersonDetails detailsField;

    private bool disabledField;

    private bool lockedField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public SsoPrincipalId id
    {
        get
        {
            return this.idField;
        }
        set
        {
            this.idField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public SsoPrincipalId alias
    {
        get
        {
            return this.aliasField;
        }
        set
        {
            this.aliasField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public SsoAdminPersonDetails details
    {
        get
        {
            return this.detailsField;
        }
        set
        {
            this.detailsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public bool disabled
    {
        get
        {
            return this.disabledField;
        }
        set
        {
            this.disabledField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public bool locked
    {
        get
        {
            return this.lockedField;
        }
        set
        {
            this.lockedField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminPersonDetails : DynamicData
{

    private string descriptionField;

    private string emailAddressField;

    private string firstNameField;

    private string lastNameField;

    private string userPrincipalNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string emailAddress
    {
        get
        {
            return this.emailAddressField;
        }
        set
        {
            this.emailAddressField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public string firstName
    {
        get
        {
            return this.firstNameField;
        }
        set
        {
            this.firstNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public string lastName
    {
        get
        {
            return this.lastNameField;
        }
        set
        {
            this.lastNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public string userPrincipalName
    {
        get
        {
            return this.userPrincipalNameField;
        }
        set
        {
            this.userPrincipalNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminGroup : DynamicData
{

    private SsoPrincipalId idField;

    private SsoPrincipalId aliasField;

    private SsoAdminGroupDetails detailsField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public SsoPrincipalId id
    {
        get
        {
            return this.idField;
        }
        set
        {
            this.idField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public SsoPrincipalId alias
    {
        get
        {
            return this.aliasField;
        }
        set
        {
            this.aliasField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public SsoAdminGroupDetails details
    {
        get
        {
            return this.detailsField;
        }
        set
        {
            this.detailsField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminGroupDetails : DynamicData
{

    private string descriptionField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminPasswordPolicy : DynamicData
{

    private string descriptionField;

    private int prohibitedPreviousPasswordsCountField;

    private SsoAdminPasswordFormat passwordFormatField;

    private int passwordLifetimeDaysField;

    private bool passwordLifetimeDaysFieldSpecified;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int prohibitedPreviousPasswordsCount
    {
        get
        {
            return this.prohibitedPreviousPasswordsCountField;
        }
        set
        {
            this.prohibitedPreviousPasswordsCountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public SsoAdminPasswordFormat passwordFormat
    {
        get
        {
            return this.passwordFormatField;
        }
        set
        {
            this.passwordFormatField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int passwordLifetimeDays
    {
        get
        {
            return this.passwordLifetimeDaysField;
        }
        set
        {
            this.passwordLifetimeDaysField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool passwordLifetimeDaysSpecified
    {
        get
        {
            return this.passwordLifetimeDaysFieldSpecified;
        }
        set
        {
            this.passwordLifetimeDaysFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminPasswordFormat : DynamicData
{

    private SsoAdminPasswordFormatLengthRestriction lengthRestrictionField;

    private SsoAdminPasswordFormatAlphabeticRestriction alphabeticRestrictionField;

    private int minNumericCountField;

    private int minSpecialCharCountField;

    private int maxIdenticalAdjacentCharactersField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public SsoAdminPasswordFormatLengthRestriction lengthRestriction
    {
        get
        {
            return this.lengthRestrictionField;
        }
        set
        {
            this.lengthRestrictionField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public SsoAdminPasswordFormatAlphabeticRestriction alphabeticRestriction
    {
        get
        {
            return this.alphabeticRestrictionField;
        }
        set
        {
            this.alphabeticRestrictionField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int minNumericCount
    {
        get
        {
            return this.minNumericCountField;
        }
        set
        {
            this.minNumericCountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int minSpecialCharCount
    {
        get
        {
            return this.minSpecialCharCountField;
        }
        set
        {
            this.minSpecialCharCountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int maxIdenticalAdjacentCharacters
    {
        get
        {
            return this.maxIdenticalAdjacentCharactersField;
        }
        set
        {
            this.maxIdenticalAdjacentCharactersField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminPasswordFormatLengthRestriction : DynamicData
{

    private int minLengthField;

    private int maxLengthField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int minLength
    {
        get
        {
            return this.minLengthField;
        }
        set
        {
            this.minLengthField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int maxLength
    {
        get
        {
            return this.maxLengthField;
        }
        set
        {
            this.maxLengthField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminPasswordFormatAlphabeticRestriction : DynamicData
{

    private int minAlphabeticCountField;

    private int minUppercaseCountField;

    private int minLowercaseCountField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int minAlphabeticCount
    {
        get
        {
            return this.minAlphabeticCountField;
        }
        set
        {
            this.minAlphabeticCountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int minUppercaseCount
    {
        get
        {
            return this.minUppercaseCountField;
        }
        set
        {
            this.minUppercaseCountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int minLowercaseCount
    {
        get
        {
            return this.minLowercaseCountField;
        }
        set
        {
            this.minLowercaseCountField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminPasswordExpirationConfig : DynamicData
{

    private bool emailNotificationEnabledField;

    private string emailFromField;

    private string emailSubjectField;

    private int[] notificationDaysField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public bool emailNotificationEnabled
    {
        get
        {
            return this.emailNotificationEnabledField;
        }
        set
        {
            this.emailNotificationEnabledField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string emailFrom
    {
        get
        {
            return this.emailFromField;
        }
        set
        {
            this.emailFromField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public string emailSubject
    {
        get
        {
            return this.emailSubjectField;
        }
        set
        {
            this.emailSubjectField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("notificationDays", Order=3)]
    public int[] notificationDays
    {
        get
        {
            return this.notificationDaysField;
        }
        set
        {
            this.notificationDaysField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminMailContent : DynamicData
{

    private string fromField;

    private string toField;

    private string subjectField;

    private string contentField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string from
    {
        get
        {
            return this.fromField;
        }
        set
        {
            this.fromField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string to
    {
        get
        {
            return this.toField;
        }
        set
        {
            this.toField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public string subject
    {
        get
        {
            return this.subjectField;
        }
        set
        {
            this.subjectField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public string content
    {
        get
        {
            return this.contentField;
        }
        set
        {
            this.contentField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminLockoutPolicy : DynamicData
{

    private string descriptionField;

    private int maxFailedAttemptsField;

    private int failedAttemptsField;

    private long failedAttemptIntervalSecField;

    private long autoUnlockIntervalSecField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int maxFailedAttempts
    {
        get
        {
            return this.maxFailedAttemptsField;
        }
        set
        {
            this.maxFailedAttemptsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int failedAttempts
    {
        get
        {
            return this.failedAttemptsField;
        }
        set
        {
            this.failedAttemptsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public long failedAttemptIntervalSec
    {
        get
        {
            return this.failedAttemptIntervalSecField;
        }
        set
        {
            this.failedAttemptIntervalSecField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public long autoUnlockIntervalSec
    {
        get
        {
            return this.autoUnlockIntervalSecField;
        }
        set
        {
            this.autoUnlockIntervalSecField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminIdentitySources : DynamicData
{

    private SsoAdminIdentitySource[] allField;

    private SsoAdminIdentitySource systemField;

    private SsoAdminIdentitySource localOSField;

    private SsoAdminLdapIdentitySource[] ldapsField;

    private SsoAdminIdentitySource nativeADField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("all", Order=0)]
    public SsoAdminIdentitySource[] all
    {
        get
        {
            return this.allField;
        }
        set
        {
            this.allField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public SsoAdminIdentitySource system
    {
        get
        {
            return this.systemField;
        }
        set
        {
            this.systemField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public SsoAdminIdentitySource localOS
    {
        get
        {
            return this.localOSField;
        }
        set
        {
            this.localOSField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("ldaps", Order=3)]
    public SsoAdminLdapIdentitySource[] ldaps
    {
        get
        {
            return this.ldapsField;
        }
        set
        {
            this.ldapsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public SsoAdminIdentitySource nativeAD
    {
        get
        {
            return this.nativeADField;
        }
        set
        {
            this.nativeADField = value;
        }
    }
}

/// <remarks/>
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminLdapIdentitySource))]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminIdentitySource : DynamicData
{

    private string nameField;

    private SsoAdminDomain[] domainsField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string name
    {
        get
        {
            return this.nameField;
        }
        set
        {
            this.nameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("domains", Order=1)]
    public SsoAdminDomain[] domains
    {
        get
        {
            return this.domainsField;
        }
        set
        {
            this.domainsField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminDomain : DynamicData
{

    private string nameField;

    private string aliasField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string name
    {
        get
        {
            return this.nameField;
        }
        set
        {
            this.nameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string alias
    {
        get
        {
            return this.aliasField;
        }
        set
        {
            this.aliasField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminLdapIdentitySource : SsoAdminIdentitySource
{

    private string typeField;

    private SsoAdminLdapIdentitySourceDetails detailsField;

    private SsoAdminLdapIdentitySourceAuthenticationDetails authenticationDetailsField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string type
    {
        get
        {
            return this.typeField;
        }
        set
        {
            this.typeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public SsoAdminLdapIdentitySourceDetails details
    {
        get
        {
            return this.detailsField;
        }
        set
        {
            this.detailsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public SsoAdminLdapIdentitySourceAuthenticationDetails authenticationDetails
    {
        get
        {
            return this.authenticationDetailsField;
        }
        set
        {
            this.authenticationDetailsField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminLdapIdentitySourceDetails : DynamicData
{

    private string friendlyNameField;

    private string userBaseDnField;

    private string groupBaseDnField;

    private string primaryUrlField;

    private string failoverUrlField;

    private int searchTimeoutSecondsField;

    private bool isSiteAffinityEnabledField;

    private bool isSiteAffinityEnabledFieldSpecified;

    private string[] certificatesField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string friendlyName
    {
        get
        {
            return this.friendlyNameField;
        }
        set
        {
            this.friendlyNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string userBaseDn
    {
        get
        {
            return this.userBaseDnField;
        }
        set
        {
            this.userBaseDnField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public string groupBaseDn
    {
        get
        {
            return this.groupBaseDnField;
        }
        set
        {
            this.groupBaseDnField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=3)]
    public string primaryUrl
    {
        get
        {
            return this.primaryUrlField;
        }
        set
        {
            this.primaryUrlField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=4)]
    public string failoverUrl
    {
        get
        {
            return this.failoverUrlField;
        }
        set
        {
            this.failoverUrlField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int searchTimeoutSeconds
    {
        get
        {
            return this.searchTimeoutSecondsField;
        }
        set
        {
            this.searchTimeoutSecondsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=6)]
    public bool isSiteAffinityEnabled
    {
        get
        {
            return this.isSiteAffinityEnabledField;
        }
        set
        {
            this.isSiteAffinityEnabledField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool isSiteAffinityEnabledSpecified
    {
        get
        {
            return this.isSiteAffinityEnabledFieldSpecified;
        }
        set
        {
            this.isSiteAffinityEnabledFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("certificates", Order=7)]
    public string[] certificates
    {
        get
        {
            return this.certificatesField;
        }
        set
        {
            this.certificatesField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminLdapIdentitySourceAuthenticationDetails : DynamicData
{

    private string authenticationTypeField;

    private string usernameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string authenticationType
    {
        get
        {
            return this.authenticationTypeField;
        }
        set
        {
            this.authenticationTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string username
    {
        get
        {
            return this.usernameField;
        }
        set
        {
            this.usernameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminIdentitySourceManagementServiceAuthenticationCredentials : DynamicData
{

    private string usernameField;

    private string passwordField;

    private bool useMachineAccountField;

    private bool useMachineAccountFieldSpecified;

    private string spnField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string username
    {
        get
        {
            return this.usernameField;
        }
        set
        {
            this.usernameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string password
    {
        get
        {
            return this.passwordField;
        }
        set
        {
            this.passwordField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public bool useMachineAccount
    {
        get
        {
            return this.useMachineAccountField;
        }
        set
        {
            this.useMachineAccountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool useMachineAccountSpecified
    {
        get
        {
            return this.useMachineAccountFieldSpecified;
        }
        set
        {
            this.useMachineAccountFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public string spn
    {
        get
        {
            return this.spnField;
        }
        set
        {
            this.spnField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminServiceEndpoint : DynamicData
{

    private string nameField;

    private string endpointField;

    private string bindingField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string name
    {
        get
        {
            return this.nameField;
        }
        set
        {
            this.nameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string endpoint
    {
        get
        {
            return this.endpointField;
        }
        set
        {
            this.endpointField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public string binding
    {
        get
        {
            return this.bindingField;
        }
        set
        {
            this.bindingField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminIDPConfiguration : DynamicData
{

    private string entityIDField;

    private string[] nameIDFormatsField;

    private SsoAdminServiceEndpoint[] ssoServicesField;

    private SsoAdminServiceEndpoint[] sloServicesField;

    private string[] signingCertificateChainField;

    private SsoAdminConfigurationManagementServiceAttributeConfig[] subjectFormatMappingsField;

    private bool isJitEnabledField;

    private bool isJitEnabledFieldSpecified;

    private SsoAdminConfigurationManagementServiceTokenClaimGroupMapping[] tokenClaimGroupMappingsField;

    private string upnSuffixField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string entityID
    {
        get
        {
            return this.entityIDField;
        }
        set
        {
            this.entityIDField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("nameIDFormats", Order=1)]
    public string[] nameIDFormats
    {
        get
        {
            return this.nameIDFormatsField;
        }
        set
        {
            this.nameIDFormatsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("ssoServices", Order=2)]
    public SsoAdminServiceEndpoint[] ssoServices
    {
        get
        {
            return this.ssoServicesField;
        }
        set
        {
            this.ssoServicesField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("sloServices", Order=3)]
    public SsoAdminServiceEndpoint[] sloServices
    {
        get
        {
            return this.sloServicesField;
        }
        set
        {
            this.sloServicesField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("signingCertificateChain", Order=4)]
    public string[] signingCertificateChain
    {
        get
        {
            return this.signingCertificateChainField;
        }
        set
        {
            this.signingCertificateChainField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("subjectFormatMappings", Order=5)]
    public SsoAdminConfigurationManagementServiceAttributeConfig[] subjectFormatMappings
    {
        get
        {
            return this.subjectFormatMappingsField;
        }
        set
        {
            this.subjectFormatMappingsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=6)]
    public bool isJitEnabled
    {
        get
        {
            return this.isJitEnabledField;
        }
        set
        {
            this.isJitEnabledField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool isJitEnabledSpecified
    {
        get
        {
            return this.isJitEnabledFieldSpecified;
        }
        set
        {
            this.isJitEnabledFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("tokenClaimGroupMappings", Order=7)]
    public SsoAdminConfigurationManagementServiceTokenClaimGroupMapping[] tokenClaimGroupMappings
    {
        get
        {
            return this.tokenClaimGroupMappingsField;
        }
        set
        {
            this.tokenClaimGroupMappingsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=8)]
    public string upnSuffix
    {
        get
        {
            return this.upnSuffixField;
        }
        set
        {
            this.upnSuffixField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminExternalDomainAuthenticationDetails : DynamicData
{

    private string authenticationTypeField;

    private string usernameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string authenticationType
    {
        get
        {
            return this.authenticationTypeField;
        }
        set
        {
            this.authenticationTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string username
    {
        get
        {
            return this.usernameField;
        }
        set
        {
            this.usernameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminExternalDomainAttributeMapping : DynamicData
{

    private string attributeIdField;

    private string attributeNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string attributeId
    {
        get
        {
            return this.attributeIdField;
        }
        set
        {
            this.attributeIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string attributeName
    {
        get
        {
            return this.attributeNameField;
        }
        set
        {
            this.attributeNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminExternalDomainObjectMapping : DynamicData
{

    private string objectIdField;

    private string objectClassField;

    private SsoAdminExternalDomainAttributeMapping[] attributeMappingsField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string objectId
    {
        get
        {
            return this.objectIdField;
        }
        set
        {
            this.objectIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string objectClass
    {
        get
        {
            return this.objectClassField;
        }
        set
        {
            this.objectClassField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("attributeMappings", Order=2)]
    public SsoAdminExternalDomainAttributeMapping[] attributeMappings
    {
        get
        {
            return this.attributeMappingsField;
        }
        set
        {
            this.attributeMappingsField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminExternalDomainSchemaDetails : DynamicData
{

    private SsoAdminExternalDomainObjectMapping[] objectMappingsField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("objectMappings", Order=0)]
    public SsoAdminExternalDomainObjectMapping[] objectMappings
    {
        get
        {
            return this.objectMappingsField;
        }
        set
        {
            this.objectMappingsField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminExternalDomainDetails : DynamicData
{

    private string friendlyNameField;

    private string userBaseDnField;

    private string groupBaseDnField;

    private string primaryUrlField;

    private string failoverUrlField;

    private int searchTimeoutSecondsField;

    private SsoAdminExternalDomainSchemaDetails schemaDetailsField;

    private string[] upnSuffixesField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string friendlyName
    {
        get
        {
            return this.friendlyNameField;
        }
        set
        {
            this.friendlyNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string userBaseDn
    {
        get
        {
            return this.userBaseDnField;
        }
        set
        {
            this.userBaseDnField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public string groupBaseDn
    {
        get
        {
            return this.groupBaseDnField;
        }
        set
        {
            this.groupBaseDnField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=3)]
    public string primaryUrl
    {
        get
        {
            return this.primaryUrlField;
        }
        set
        {
            this.primaryUrlField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=4)]
    public string failoverUrl
    {
        get
        {
            return this.failoverUrlField;
        }
        set
        {
            this.failoverUrlField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int searchTimeoutSeconds
    {
        get
        {
            return this.searchTimeoutSecondsField;
        }
        set
        {
            this.searchTimeoutSecondsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=6)]
    public SsoAdminExternalDomainSchemaDetails schemaDetails
    {
        get
        {
            return this.schemaDetailsField;
        }
        set
        {
            this.schemaDetailsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("upnSuffixes", Order=7)]
    public string[] upnSuffixes
    {
        get
        {
            return this.upnSuffixesField;
        }
        set
        {
            this.upnSuffixesField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminExternalDomain : DynamicData
{

    private string typeField;

    private string nameField;

    private string aliasField;

    private SsoAdminExternalDomainDetails detailsField;

    private SsoAdminExternalDomainAuthenticationDetails authenticationDetailsField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string type
    {
        get
        {
            return this.typeField;
        }
        set
        {
            this.typeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string name
    {
        get
        {
            return this.nameField;
        }
        set
        {
            this.nameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public string alias
    {
        get
        {
            return this.aliasField;
        }
        set
        {
            this.aliasField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public SsoAdminExternalDomainDetails details
    {
        get
        {
            return this.detailsField;
        }
        set
        {
            this.detailsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public SsoAdminExternalDomainAuthenticationDetails authenticationDetails
    {
        get
        {
            return this.authenticationDetailsField;
        }
        set
        {
            this.authenticationDetailsField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminDomains : DynamicData
{

    private SsoAdminExternalDomain[] externalDomainsField;

    private string systemDomainNameField;

    private string[] systemDomainUpnSuffixesField;

    private string localOSDomainNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("externalDomains", Order=0)]
    public SsoAdminExternalDomain[] externalDomains
    {
        get
        {
            return this.externalDomainsField;
        }
        set
        {
            this.externalDomainsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string systemDomainName
    {
        get
        {
            return this.systemDomainNameField;
        }
        set
        {
            this.systemDomainNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("systemDomainUpnSuffixes", Order=2)]
    public string[] systemDomainUpnSuffixes
    {
        get
        {
            return this.systemDomainUpnSuffixesField;
        }
        set
        {
            this.systemDomainUpnSuffixesField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public string localOSDomainName
    {
        get
        {
            return this.localOSDomainNameField;
        }
        set
        {
            this.localOSDomainNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminDomainManagementServiceAuthenticationCredentails : DynamicData
{

    private string usernameField;

    private string passwordField;

    private bool useMachineAccountField;

    private bool useMachineAccountFieldSpecified;

    private string spnField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string username
    {
        get
        {
            return this.usernameField;
        }
        set
        {
            this.usernameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string password
    {
        get
        {
            return this.passwordField;
        }
        set
        {
            this.passwordField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public bool useMachineAccount
    {
        get
        {
            return this.useMachineAccountField;
        }
        set
        {
            this.useMachineAccountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool useMachineAccountSpecified
    {
        get
        {
            return this.useMachineAccountFieldSpecified;
        }
        set
        {
            this.useMachineAccountFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public string spn
    {
        get
        {
            return this.spnField;
        }
        set
        {
            this.spnField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminClientCertPolicy : DynamicData
{

    private bool enabledField;

    private bool ocspEnabledField;

    private bool useCRLAsFailOverField;

    private bool sendOCSPNonceField;

    private string ocspUrlField;

    private string ocspResponderSigningCertField;

    private bool useInCertCRLField;

    private string crlUrlField;

    private int crlCacheSizeField;

    private string[] oidsField;

    private string[] trustedCAsField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public bool enabled
    {
        get
        {
            return this.enabledField;
        }
        set
        {
            this.enabledField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public bool ocspEnabled
    {
        get
        {
            return this.ocspEnabledField;
        }
        set
        {
            this.ocspEnabledField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public bool useCRLAsFailOver
    {
        get
        {
            return this.useCRLAsFailOverField;
        }
        set
        {
            this.useCRLAsFailOverField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public bool sendOCSPNonce
    {
        get
        {
            return this.sendOCSPNonceField;
        }
        set
        {
            this.sendOCSPNonceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public string ocspUrl
    {
        get
        {
            return this.ocspUrlField;
        }
        set
        {
            this.ocspUrlField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public string ocspResponderSigningCert
    {
        get
        {
            return this.ocspResponderSigningCertField;
        }
        set
        {
            this.ocspResponderSigningCertField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=6)]
    public bool useInCertCRL
    {
        get
        {
            return this.useInCertCRLField;
        }
        set
        {
            this.useInCertCRLField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public string crlUrl
    {
        get
        {
            return this.crlUrlField;
        }
        set
        {
            this.crlUrlField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=8)]
    public int crlCacheSize
    {
        get
        {
            return this.crlCacheSizeField;
        }
        set
        {
            this.crlCacheSizeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("oids", Order=9)]
    public string[] oids
    {
        get
        {
            return this.oidsField;
        }
        set
        {
            this.oidsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("trustedCAs", Order=10)]
    public string[] trustedCAs
    {
        get
        {
            return this.trustedCAsField;
        }
        set
        {
            this.trustedCAsField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminAuthnPolicy : DynamicData
{

    private bool passwordAuthnEnabledField;

    private bool windowsAuthEnabledField;

    private bool certAuthEnabledField;

    private SsoAdminClientCertPolicy clientCertPolicyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public bool PasswordAuthnEnabled
    {
        get
        {
            return this.passwordAuthnEnabledField;
        }
        set
        {
            this.passwordAuthnEnabledField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public bool WindowsAuthEnabled
    {
        get
        {
            return this.windowsAuthEnabledField;
        }
        set
        {
            this.windowsAuthEnabledField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public bool CertAuthEnabled
    {
        get
        {
            return this.certAuthEnabledField;
        }
        set
        {
            this.certAuthEnabledField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public SsoAdminClientCertPolicy clientCertPolicy
    {
        get
        {
            return this.clientCertPolicyField;
        }
        set
        {
            this.clientCertPolicyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminAuthenticationAccountInfo : DynamicData
{

    private string userNameField;

    private string spnField;

    private bool useMachineAccountField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string userName
    {
        get
        {
            return this.userNameField;
        }
        set
        {
            this.userNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string spn
    {
        get
        {
            return this.spnField;
        }
        set
        {
            this.spnField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public bool useMachineAccount
    {
        get
        {
            return this.useMachineAccountField;
        }
        set
        {
            this.useMachineAccountField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminActiveDirectoryJoinInfo : DynamicData
{

    private string joinStatusField;

    private string nameField;

    private string aliasField;

    private string dnField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string joinStatus
    {
        get
        {
            return this.joinStatusField;
        }
        set
        {
            this.joinStatusField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string name
    {
        get
        {
            return this.nameField;
        }
        set
        {
            this.nameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public string alias
    {
        get
        {
            return this.aliasField;
        }
        set
        {
            this.aliasField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public string dn
    {
        get
        {
            return this.dnField;
        }
        set
        {
            this.dnField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class LocalizedMethodFault : DynamicData
{

    private MethodFault faultField;

    private string localizedMessageField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public MethodFault fault
    {
        get
        {
            return this.faultField;
        }
        set
        {
            this.faultField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string localizedMessage
    {
        get
        {
            return this.localizedMessageField;
        }
        set
        {
            this.localizedMessageField = value;
        }
    }
}

/// <remarks/>
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultInvalidPrincipalFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultSmtpConfigNotSetFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultPasswordPolicyViolationFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultNoSuchRelyingPartyFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultNoSuchExternalSTSConfigFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultNoSuchConfigFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultInvalidPasswordPolicyFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultGroupCyclicDependencyFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultExtraneousCertsInCertChainFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultExternalSTSExtraneousCertsInCertChainFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultExternalSTSCertChainInvalidTrustedPathFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultDuplicateSolutionCertificateFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultDuplicateDomainNameFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultDuplicateDataFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultDomainNotFoundFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultDirectoryServiceConnectionFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultCertificateDeletionFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultCertChainInvalidTrustedPathFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultADIDSAlreadyExistFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultADDomainUnknownDomainFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultADDomainNotJoinedFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultADDomainAlreadyJoinedFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultADDomainAccessDeniedFault))]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoFaultServiceFault : MethodFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoFaultInvalidPrincipalFault : SsoFaultServiceFault
{

    private string principalField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string principal
    {
        get
        {
            return this.principalField;
        }
        set
        {
            this.principalField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultSmtpConfigNotSetFault : SsoFaultServiceFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultPasswordPolicyViolationFault : SsoFaultServiceFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultNoSuchRelyingPartyFault : SsoFaultServiceFault
{

    private string relyingPartyNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string relyingPartyName
    {
        get
        {
            return this.relyingPartyNameField;
        }
        set
        {
            this.relyingPartyNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultNoSuchExternalSTSConfigFault : SsoFaultServiceFault
{

    private string issuerNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string issuerName
    {
        get
        {
            return this.issuerNameField;
        }
        set
        {
            this.issuerNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultNoSuchConfigFault : SsoFaultServiceFault
{

    private string issuerNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string issuerName
    {
        get
        {
            return this.issuerNameField;
        }
        set
        {
            this.issuerNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultInvalidPasswordPolicyFault : SsoFaultServiceFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultGroupCyclicDependencyFault : SsoFaultServiceFault
{

    private string groupBeingAddedField;

    private string existingGroupField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string groupBeingAdded
    {
        get
        {
            return this.groupBeingAddedField;
        }
        set
        {
            this.groupBeingAddedField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string existingGroup
    {
        get
        {
            return this.existingGroupField;
        }
        set
        {
            this.existingGroupField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultExtraneousCertsInCertChainFault : SsoFaultServiceFault
{

    private string issuerNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string issuerName
    {
        get
        {
            return this.issuerNameField;
        }
        set
        {
            this.issuerNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultExternalSTSExtraneousCertsInCertChainFault : SsoFaultServiceFault
{

    private string issuerNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string issuerName
    {
        get
        {
            return this.issuerNameField;
        }
        set
        {
            this.issuerNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultExternalSTSCertChainInvalidTrustedPathFault : SsoFaultServiceFault
{

    private string issuerNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string issuerName
    {
        get
        {
            return this.issuerNameField;
        }
        set
        {
            this.issuerNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultDuplicateSolutionCertificateFault : SsoFaultServiceFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultDuplicateDomainNameFault : SsoFaultServiceFault
{

    private string domainNameField;

    private string domainAliasField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string domainName
    {
        get
        {
            return this.domainNameField;
        }
        set
        {
            this.domainNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string domainAlias
    {
        get
        {
            return this.domainAliasField;
        }
        set
        {
            this.domainAliasField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultDuplicateDataFault : SsoFaultServiceFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultDomainNotFoundFault : SsoFaultServiceFault
{

    private string domainNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string domainName
    {
        get
        {
            return this.domainNameField;
        }
        set
        {
            this.domainNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultDirectoryServiceConnectionFault : SsoFaultServiceFault
{

    private string uriField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=0)]
    public string uri
    {
        get
        {
            return this.uriField;
        }
        set
        {
            this.uriField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultCertificateDeletionFault : SsoFaultServiceFault
{

    private string certificateField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string certificate
    {
        get
        {
            return this.certificateField;
        }
        set
        {
            this.certificateField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultCertChainInvalidTrustedPathFault : SsoFaultServiceFault
{

    private string issuerNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string issuerName
    {
        get
        {
            return this.issuerNameField;
        }
        set
        {
            this.issuerNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultADIDSAlreadyExistFault : SsoFaultServiceFault
{

    private string domainNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string domainName
    {
        get
        {
            return this.domainNameField;
        }
        set
        {
            this.domainNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultADDomainUnknownDomainFault : SsoFaultServiceFault
{

    private string domainField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string domain
    {
        get
        {
            return this.domainField;
        }
        set
        {
            this.domainField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultADDomainNotJoinedFault : SsoFaultServiceFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultADDomainAlreadyJoinedFault : SsoFaultServiceFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultADDomainAccessDeniedFault : SsoFaultServiceFault
{

    private string domainField;

    private string usernameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string domain
    {
        get
        {
            return this.domainField;
        }
        set
        {
            this.domainField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string username
    {
        get
        {
            return this.usernameField;
        }
        set
        {
            this.usernameField = value;
        }
    }
}

/// <remarks/>
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultNoDomainSearchPermission))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoFaultInternalFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultNativeADRegistrationFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultLocalOSDomainRegistrationFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultInvalidProviderFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultHostNotJoinedRequiredDomainFault))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SsoAdminFaultDomainManagerFault))]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoFaultRuntimeServiceFault : RuntimeFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoFaultNoDomainSearchPermission : SsoFaultRuntimeServiceFault
{

    private string domainNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string domainName
    {
        get
        {
            return this.domainNameField;
        }
        set
        {
            this.domainNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoFaultInternalFault : SsoFaultRuntimeServiceFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultNativeADRegistrationFault : SsoFaultRuntimeServiceFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultLocalOSDomainRegistrationFault : SsoFaultRuntimeServiceFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultInvalidProviderFault : SsoFaultRuntimeServiceFault
{

    private string fieldNameField;

    private string fieldValueField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string fieldName
    {
        get
        {
            return this.fieldNameField;
        }
        set
        {
            this.fieldNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string fieldValue
    {
        get
        {
            return this.fieldValueField;
        }
        set
        {
            this.fieldValueField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultHostNotJoinedRequiredDomainFault : SsoFaultRuntimeServiceFault
{

    private string requiredDomainNameField;

    private string joinedDomainNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string requiredDomainName
    {
        get
        {
            return this.requiredDomainNameField;
        }
        set
        {
            this.requiredDomainNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string joinedDomainName
    {
        get
        {
            return this.joinedDomainNameField;
        }
        set
        {
            this.joinedDomainNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoAdminFaultDomainManagerFault : SsoFaultRuntimeServiceFault
{

    private string domainNameField;

    private int errorCodeField;

    private bool errorCodeFieldSpecified;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string domainName
    {
        get
        {
            return this.domainNameField;
        }
        set
        {
            this.domainNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int errorCode
    {
        get
        {
            return this.errorCodeField;
        }
        set
        {
            this.errorCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool errorCodeSpecified
    {
        get
        {
            return this.errorCodeFieldSpecified;
        }
        set
        {
            this.errorCodeFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class UnexpectedFault : RuntimeFault
{

    private string faultNameField;

    private LocalizedMethodFault faultField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string faultName
    {
        get
        {
            return this.faultNameField;
        }
        set
        {
            this.faultNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public LocalizedMethodFault fault
    {
        get
        {
            return this.faultField;
        }
        set
        {
            this.faultField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class SystemError : RuntimeFault
{

    private string reasonField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string reason
    {
        get
        {
            return this.reasonField;
        }
        set
        {
            this.reasonField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class RequestCanceled : RuntimeFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class NotSupported : RuntimeFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class NotImplemented : RuntimeFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class NotEnoughLicenses : RuntimeFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class ManagedObjectNotFound : RuntimeFault
{

    private ManagedObjectReference objField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public ManagedObjectReference obj
    {
        get
        {
            return this.objField;
        }
        set
        {
            this.objField = value;
        }
    }
}

/// <remarks/>
[System.Xml.Serialization.XmlIncludeAttribute(typeof(MethodNotFound))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidType))]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class InvalidRequest : RuntimeFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class MethodNotFound : InvalidRequest
{

    private ManagedObjectReference receiverField;

    private string methodField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public ManagedObjectReference receiver
    {
        get
        {
            return this.receiverField;
        }
        set
        {
            this.receiverField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string method
    {
        get
        {
            return this.methodField;
        }
        set
        {
            this.methodField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class InvalidType : InvalidRequest
{

    private string argumentField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string argument
    {
        get
        {
            return this.argumentField;
        }
        set
        {
            this.argumentField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class InvalidArgument : RuntimeFault
{

    private string invalidPropertyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public string invalidProperty
    {
        get
        {
            return this.invalidPropertyField;
        }
        set
        {
            this.invalidPropertyField = value;
        }
    }
}

/// <remarks/>
[System.Xml.Serialization.XmlIncludeAttribute(typeof(HostNotReachable))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(HostNotConnected))]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class HostCommunication : RuntimeFault
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class HostNotReachable : HostCommunication
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace= "urn:sso")]
public partial class HostNotConnected : HostCommunication
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoFaultNotAuthenticated : SecurityError
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:sso")]
public partial class SsoFaultNoPermission : SecurityError
{
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ServiceModel.ServiceContractAttribute(Namespace="urn:sso", ConfigurationName="SsoPortType")]
public interface SsoPortType
{

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task LoginAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(InvalidRequest), Action="urn:sso/version3_5", Name="InvalidRequestFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task LogoutAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<string> SetLocaleAsync(ManagedObjectReference _this, string locale);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<string> GetLocaleAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> AddCertificateAsync(ManagedObjectReference _this, string certificate);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<GetAllCertificatesResponse> GetAllCertificatesAsync(GetAllCertificatesRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<string> FindCertificateAsync(ManagedObjectReference _this, string fingerprint);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> DeleteCertificateAsync(ManagedObjectReference _this, string fingerprint);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<GetComputersResponse> GetComputersAsync(GetComputersRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<GetKnownCertificateChainsResponse> GetKnownCertificateChainsAsync(GetKnownCertificateChainsRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<GetTrustedCertificatesResponse> GetTrustedCertificatesAsync(GetTrustedCertificatesRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<GetIssuersCertificatesResponse> GetIssuersCertificatesAsync(GetIssuersCertificatesRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultExternalSTSCertChainInvalidTrustedPathFault), Action="urn:sso/version3_5", Name="SsoAdminFaultExternalSTSCertChainInvalidTrustedPathFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultExternalSTSExtraneousCertsInCertChainFault), Action="urn:sso/version3_5", Name="SsoAdminFaultExternalSTSExtraneousCertsInCertChainFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task ImportTrustedSTSConfigurationAsync(ManagedObjectReference _this, SsoAdminTrustedSTSConfig stsConfig);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultExternalSTSCertChainInvalidTrustedPathFault), Action="urn:sso/version3_5", Name="SsoAdminFaultExternalSTSCertChainInvalidTrustedPathFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultExternalSTSExtraneousCertsInCertChainFault), Action="urn:sso/version3_5", Name="SsoAdminFaultExternalSTSExtraneousCertsInCertChainFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task RemoveTrustedSTSConfigurationAsync(ManagedObjectReference _this, string issuerName);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultExternalSTSCertChainInvalidTrustedPathFault), Action="urn:sso/version3_5", Name="SsoAdminFaultExternalSTSCertChainInvalidTrustedPathFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultExternalSTSExtraneousCertsInCertChainFault), Action="urn:sso/version3_5", Name="SsoAdminFaultExternalSTSExtraneousCertsInCertChainFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task ImportExternalIDPConfigurationAsync(ManagedObjectReference _this, string externalIDPConfigDoc);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultCertChainInvalidTrustedPathFault), Action="urn:sso/version3_5", Name="SsoAdminFaultCertChainInvalidTrustedPathFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultExtraneousCertsInCertChainFault), Action="urn:sso/version3_5", Name="SsoAdminFaultExtraneousCertsInCertChainFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task CreateExternalIDPConfigurationAsync(ManagedObjectReference _this, SsoAdminIDPConfiguration config);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultNoSuchConfigFault), Action="urn:sso/version3_5", Name="SsoAdminFaultNoSuchConfigFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminIDPConfiguration> GetExternalIDPConfigurationAsync(ManagedObjectReference _this, string entityID);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultCertChainInvalidTrustedPathFault), Action="urn:sso/version3_5", Name="SsoAdminFaultCertChainInvalidTrustedPathFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultExtraneousCertsInCertChainFault), Action="urn:sso/version3_5", Name="SsoAdminFaultExtraneousCertsInCertChainFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SetExternalIDPConfigurationAsync(ManagedObjectReference _this, SsoAdminIDPConfiguration config);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultNoSuchConfigFault), Action="urn:sso/version3_5", Name="SsoAdminFaultNoSuchConfigFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task DeleteExternalIDPConfigurationAsync(ManagedObjectReference _this, string entityID);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultNoSuchConfigFault), Action="urn:sso/version3_5", Name="SsoAdminFaultNoSuchConfigFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task DeleteExternalIDPConfigurationAndUsersAsync(ManagedObjectReference _this, string entityID);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<EnumerateExternalIDPEntityIDsResponse> EnumerateExternalIDPEntityIDsAsync(EnumerateExternalIDPEntityIDsRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<GetExternalIdpTrustedCertificateChainsResponse> GetExternalIdpTrustedCertificateChainsAsync(GetExternalIdpTrustedCertificateChainsRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminConfigurationManagementServiceCertificateChain> GetExternalIdpTrustedCertificateChainAsync(ManagedObjectReference _this, string entityId);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultCertificateDeletionFault), Action="urn:sso/version3_5", Name="SsoAdminFaultCertificateDeletionFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> DeleteTrustedCertificateAsync(ManagedObjectReference _this, string fingerprint);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SetNewSignerIdentityAsync(ManagedObjectReference _this, string signingKey, SsoAdminConfigurationManagementServiceCertificateChain signingCertificateChain);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SetSignerIdentityAsync(ManagedObjectReference _this, SsoPrincipalId adminUser, string adminPass, string signingKey, SsoAdminConfigurationManagementServiceCertificateChain signingCertificateChain);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<long> GetClockToleranceAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SetClockToleranceAsync(ManagedObjectReference _this, long milliseconds);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<int> GetDelegationCountAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SetDelegationCountAsync(ManagedObjectReference _this, int delegationCount);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<int> GetRenewCountAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SetRenewCountAsync(ManagedObjectReference _this, int renewCount);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<long> GetMaximumBearerTokenLifetimeAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SetMaximumBearerTokenLifetimeAsync(ManagedObjectReference _this, long maxLifetime);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<long> GetMaximumHoKTokenLifetimeAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SetMaximumHoKTokenLifetimeAsync(ManagedObjectReference _this, long maxLifetime);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminPasswordExpirationConfig> GetPasswordExpirationConfigurationAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task UpdatePasswordExpirationConfigurationAsync(ManagedObjectReference _this, SsoAdminPasswordExpirationConfig config);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task ImportSAMLMetadataAsync(ManagedObjectReference _this, string samlConfigDoc);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultNoSuchRelyingPartyFault), Action="urn:sso/version3_5", Name="SsoAdminFaultNoSuchRelyingPartyFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task DeleteRelyingPartyAsync(ManagedObjectReference _this, string rpName);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<string> GetIssuerNameAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultNoSuchConfigFault), Action="urn:sso/version3_5", Name="SsoAdminFaultNoSuchConfigFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> IsExternalIDPJitEnabledAsync(ManagedObjectReference _this, string entityID);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SetExternalIDPJitAttributeAsync(ManagedObjectReference _this, string entityID, bool enableJit);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SetAuthnPolicyAsync(ManagedObjectReference _this, SsoAdminAuthnPolicy policy);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminAuthnPolicy> GetAuthnPolicyAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInternalFault), Action="urn:sso/version3_5", Name="SsoFaultInternalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> IsMultiSiteDeploymentAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInternalFault), Action="urn:sso/version3_5", Name="SsoFaultInternalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<RetrieveHaBackupConfigurationPackageResponse> RetrieveHaBackupConfigurationPackageAsync(RetrieveHaBackupConfigurationPackageRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInternalFault), Action="urn:sso/version3_5", Name="SsoFaultInternalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(NotSupported), Action="urn:sso/version3_5", Name="NotSupportedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<RetrieveReplicaConfigurationPackageResponse> RetrieveReplicaConfigurationPackageAsync(RetrieveReplicaConfigurationPackageRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDirectoryServiceConnectionFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDirectoryServiceConnectionFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<ProbeConnectivityResponse> ProbeConnectivityAsync(ProbeConnectivityRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDirectoryServiceConnectionFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDirectoryServiceConnectionFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDuplicateDomainNameFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDuplicateDomainNameFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task AddExternalDomainAsync(ManagedObjectReference _this, string serverType, string domainName, string domainAlias, SsoAdminExternalDomainDetails details, string authenticationType, SsoAdminDomainManagementServiceAuthenticationCredentails authnCredentials);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDuplicateDomainNameFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDuplicateDomainNameFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultLocalOSDomainRegistrationFault), Action="urn:sso/version3_5", Name="SsoAdminFaultLocalOSDomainRegistrationFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task RegisterLocalOSDomainAsync(ManagedObjectReference _this, string domainName);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminDomains> GetDomainsAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminExternalDomain> FindExternalDomainAsync(ManagedObjectReference _this, string name);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SetBrandNameAsync(ManagedObjectReference _this, string brandName);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<string> GetBrandNameAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SetLogonBannerAsync(ManagedObjectReference _this, string logonBanner);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<string> GetLogonBannerAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SetLogonBannerTitleAsync(ManagedObjectReference _this, string logonBannerTitle);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<string> GetLogonBannerTitleAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SetLogonBannerContentAsync(ManagedObjectReference _this, string logonBannerContent);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<string> GetLogonBannerContentAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SetLogonBannerCheckboxFlagAsync(ManagedObjectReference _this, bool enableLogonBannerCheckbox);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> GetLogonBannerCheckboxFlagAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task DisableLogonBannerAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> IsLogonBannerDisabledAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<string> GetSystemDomainNameAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<string> GetSystemTenantNameAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<string> GetLocalOSDomainNameAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDomainNotFoundFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDomainNotFoundFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task UpdateExternalDomainDetailsAsync(ManagedObjectReference _this, string name, SsoAdminExternalDomainDetails details);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDomainNotFoundFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDomainNotFoundFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task UpdateExternalDomainAuthnTypeAsync(ManagedObjectReference _this, string name, string authnType, SsoAdminDomainManagementServiceAuthenticationCredentails authnCredentials);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDomainNotFoundFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDomainNotFoundFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> RegisterUpnSuffixAsync(ManagedObjectReference _this, string domainName, string upnSuffix);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDomainNotFoundFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDomainNotFoundFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> UnRegisterUpnSuffixAsync(ManagedObjectReference _this, string domainName, string upnSuffix);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDomainNotFoundFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDomainNotFoundFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<GetUpnSuffixesResponse> GetUpnSuffixesAsync(GetUpnSuffixesRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDomainNotFoundFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDomainNotFoundFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task DeleteDomainAsync(ManagedObjectReference _this, string name);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<ManagedObjectReference> GetSslCertificateManagerAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDomainNotFoundFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDomainNotFoundFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDuplicateDomainNameFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDuplicateDomainNameFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<SetDefaultDomainsResponse> SetDefaultDomainsAsync(SetDefaultDomainsRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<GetDefaultDomainsResponse> GetDefaultDomainsAsync(GetDefaultDomainsRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDirectoryServiceConnectionFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDirectoryServiceConnectionFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminConfigurationManagementServiceCertificateChain> GetSslIdentityAsync(ManagedObjectReference _this, string host, int ldapsPort);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultADIDSAlreadyExistFault), Action="urn:sso/version3_5", Name="SsoAdminFaultADIDSAlreadyExistFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDirectoryServiceConnectionFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDirectoryServiceConnectionFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDuplicateDomainNameFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDuplicateDomainNameFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultInvalidProviderFault), Action="urn:sso/version3_5", Name="SsoAdminFaultInvalidProviderFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task RegisterLdapAsync(ManagedObjectReference _this, string serverType, string domainName, string domainAlias, SsoAdminLdapIdentitySourceDetails details, string authenticationType, SsoAdminIdentitySourceManagementServiceAuthenticationCredentials authnCredentials);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultADIDSAlreadyExistFault), Action="urn:sso/version3_5", Name="SsoAdminFaultADIDSAlreadyExistFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDomainManagerFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDomainManagerFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDuplicateDomainNameFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDuplicateDomainNameFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultHostNotJoinedRequiredDomainFault), Action="urn:sso/version3_5", Name="SsoAdminFaultHostNotJoinedRequiredDomainFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task RegisterActiveDirectoryAsync(ManagedObjectReference _this, string domainName, SsoAdminIdentitySourceManagementServiceAuthenticationCredentials authnCredentials, SsoAdminExternalDomainSchemaDetails schemaMapping);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDuplicateDomainNameFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDuplicateDomainNameFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultLocalOSDomainRegistrationFault), Action="urn:sso/version3_5", Name="SsoAdminFaultLocalOSDomainRegistrationFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task RegisterLocalOSAsync(ManagedObjectReference _this, string name);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminIdentitySources> GetAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminAuthenticationAccountInfo> GetActiveDirectoryAuthnAccountInfoAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<string> IdS_getSystemDomainNameAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDirectoryServiceConnectionFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDirectoryServiceConnectionFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDomainNotFoundFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDomainNotFoundFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultInvalidProviderFault), Action="urn:sso/version3_5", Name="SsoAdminFaultInvalidProviderFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task UpdateLdapAsync(ManagedObjectReference _this, string name, SsoAdminLdapIdentitySourceDetails details);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDomainManagerFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDomainManagerFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDomainNotFoundFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDomainNotFoundFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDuplicateDomainNameFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDuplicateDomainNameFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task UpdateActiveDirectoryAsync(ManagedObjectReference _this, string domainName, SsoAdminIdentitySourceManagementServiceAuthenticationCredentials authnCredentials, SsoAdminExternalDomainSchemaDetails schemaMapping);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDirectoryServiceConnectionFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDirectoryServiceConnectionFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDomainNotFoundFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDomainNotFoundFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task UpdateLdapAuthnTypeAsync(ManagedObjectReference _this, string name, string authnType, SsoAdminIdentitySourceManagementServiceAuthenticationCredentials authnCredentials);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDomainNotFoundFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDomainNotFoundFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task DeleteAsync(ManagedObjectReference _this, string name);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDomainNotFoundFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDomainNotFoundFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDuplicateDomainNameFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDuplicateDomainNameFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<IdS_setDefaultDomainsResponse> IdS_setDefaultDomainsAsync(IdS_setDefaultDomainsRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<IdS_getDefaultDomainsResponse> IdS_getDefaultDomainsAsync(IdS_getDefaultDomainsRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<ManagedObjectReference> IdS_getSslCertificateManagerAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDirectoryServiceConnectionFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDirectoryServiceConnectionFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<IdS_probeConnectivityResponse> IdS_probeConnectivityAsync(IdS_probeConnectivityRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDirectoryServiceConnectionFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDirectoryServiceConnectionFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task IdS_probeLdapConnectivityAsync(ManagedObjectReference _this, string domainName, SsoAdminIdentitySourceManagementServiceAuthenticationCredentials authnCredential, SsoAdminLdapIdentitySource identitySource);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDirectoryServiceConnectionFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDirectoryServiceConnectionFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminConfigurationManagementServiceCertificateChain> IdS_getSslIdentityAsync(ManagedObjectReference _this, string host, int ldapsPort);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminLockoutPolicy> GetLockoutPolicyAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task UpdateLockoutPolicyAsync(ManagedObjectReference _this, SsoAdminLockoutPolicy policy);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultInvalidPasswordPolicyFault), Action="urn:sso/version3_5", Name="SsoAdminFaultInvalidPasswordPolicyFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task UpdateLocalPasswordPolicyAsync(ManagedObjectReference _this, SsoAdminPasswordPolicy policy);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminPasswordPolicy> GetLocalPasswordPolicyAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoPrincipalId> LookupAsync(ManagedObjectReference _this, SsoPrincipalId id, bool isGroup);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminPersonUser> FindPersonUserAsync(ManagedObjectReference _this, SsoPrincipalId userId);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminPersonUser> FindSelfPersonUserAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminSolutionUser> FindSolutionUserAsync(ManagedObjectReference _this, string userName);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminSolutionUser> FindSolutionUserByCertDNAsync(ManagedObjectReference _this, string certDN);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminUser> FindUserAsync(ManagedObjectReference _this, SsoPrincipalId userId);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminGroup> FindGroupAsync(ManagedObjectReference _this, SsoPrincipalId groupId);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindPersonUsersResponse> FindPersonUsersAsync(FindPersonUsersRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindPersonUsersByNameResponse> FindPersonUsersByNameAsync(FindPersonUsersByNameRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindSolutionUsersResponse> FindSolutionUsersAsync(FindSolutionUsersRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindUsersResponse> FindUsersAsync(FindUsersRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminPrincipalDiscoveryServiceSearchResult> FindUserAccountAsync(ManagedObjectReference _this, string userName);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminGroup> FindGroupAccountAsync(ManagedObjectReference _this, string groupName);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindGroupsResponse> FindGroupsAsync(FindGroupsRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindGroupsByNameResponse> FindGroupsByNameAsync(FindGroupsByNameRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminPrincipalDiscoveryServiceSearchResult> FindAsync(ManagedObjectReference _this, SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria, int limit);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminPrincipalDiscoveryServiceSearchResult> FindByNameAsync(ManagedObjectReference _this, SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria, int limit);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindUsersInGroupResponse> FindUsersInGroupAsync(FindUsersInGroupRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindPersonUsersByNameInGroupResponse> FindPersonUsersByNameInGroupAsync(FindPersonUsersByNameInGroupRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindPersonUsersInGroupResponse> FindPersonUsersInGroupAsync(FindPersonUsersInGroupRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindSolutionUsersInGroupResponse> FindSolutionUsersInGroupAsync(FindSolutionUsersInGroupRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindGroupsInGroupResponse> FindGroupsInGroupAsync(FindGroupsInGroupRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindGroupsByNameInGroupResponse> FindGroupsByNameInGroupAsync(FindGroupsByNameInGroupRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindDirectParentGroupsResponse> FindDirectParentGroupsAsync(FindDirectParentGroupsRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindNestedParentGroupsResponse> FindNestedParentGroupsAsync(FindNestedParentGroupsRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindLockedUsersResponse> FindLockedUsersAsync(FindLockedUsersRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindDisabledPersonUsersResponse> FindDisabledPersonUsersAsync(FindDisabledPersonUsersRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindDisabledSolutionUsersResponse> FindDisabledSolutionUsersAsync(FindDisabledSolutionUsersRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminPersonUser> FindRegisteredExternalIDPUserAsync(ManagedObjectReference _this, SsoPrincipalId userId);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<GetImplicitGroupNamesResponse> GetImplicitGroupNamesAsync(GetImplicitGroupNamesRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultPasswordPolicyViolationFault), Action="urn:sso/version3_5", Name="SsoAdminFaultPasswordPolicyViolationFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoPrincipalId> CreateLocalPersonUserAsync(ManagedObjectReference _this, string userName, SsoAdminPersonDetails userDetails, string password);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultDuplicateSolutionCertificateFault), Action="urn:sso/version3_5", Name="SsoAdminFaultDuplicateSolutionCertificateFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoPrincipalId> CreateLocalSolutionUserAsync(ManagedObjectReference _this, string userName, SsoAdminSolutionDetails userDetails, bool external);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoPrincipalId> CreateLocalGroupAsync(ManagedObjectReference _this, string groupName, SsoAdminGroupDetails groupDetails);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task DeleteLocalPrincipalAsync(ManagedObjectReference _this, string principalName);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> RemoveFromLocalGroupAsync(ManagedObjectReference _this, SsoPrincipalId principalId, string groupName);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<RemovePrincipalsFromLocalGroupResponse> RemovePrincipalsFromLocalGroupAsync(RemovePrincipalsFromLocalGroupRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> AddUserToLocalGroupAsync(ManagedObjectReference _this, SsoPrincipalId userId, string groupName);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<AddUsersToLocalGroupResponse> AddUsersToLocalGroupAsync(AddUsersToLocalGroupRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultGroupCyclicDependencyFault), Action="urn:sso/version3_5", Name="SsoAdminFaultGroupCyclicDependencyFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> AddGroupToLocalGroupAsync(ManagedObjectReference _this, SsoPrincipalId groupId, string groupName);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultGroupCyclicDependencyFault), Action="urn:sso/version3_5", Name="SsoAdminFaultGroupCyclicDependencyFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<AddGroupsToLocalGroupResponse> AddGroupsToLocalGroupAsync(AddGroupsToLocalGroupRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoPrincipalId> UpdateLocalPersonUserDetailsAsync(ManagedObjectReference _this, string userName, SsoAdminPersonDetails userDetails);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultPasswordPolicyViolationFault), Action="urn:sso/version3_5", Name="SsoAdminFaultPasswordPolicyViolationFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task ResetLocalPersonUserPasswordAsync(ManagedObjectReference _this, string userName, string newPassword);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoPrincipalId> UpdateLocalSolutionUserDetailsAsync(ManagedObjectReference _this, string userName, SsoAdminSolutionDetails userDetails);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoPrincipalId> UpdateLocalGroupDetailsAsync(ManagedObjectReference _this, string groupName, SsoAdminGroupDetails groupDetails);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoPrincipalId> UpdateSelfLocalPersonUserDetailsAsync(ManagedObjectReference _this, SsoAdminPersonDetails userDetails);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task DeleteSelfSolutionUserAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultPasswordPolicyViolationFault), Action="urn:sso/version3_5", Name="SsoAdminFaultPasswordPolicyViolationFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task ResetSelfLocalPersonUserPasswordAsync(ManagedObjectReference _this, string newPassword);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultPasswordPolicyViolationFault), Action="urn:sso/version3_5", Name="SsoAdminFaultPasswordPolicyViolationFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(InvalidRequest), Action="urn:sso/version3_5", Name="InvalidRequestFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task ResetLocalUserPasswordAsync(ManagedObjectReference _this, string username, string currentPassword, string newPassword);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoPrincipalId> UpdateSelfLocalSolutionUserDetailsAsync(ManagedObjectReference _this, SsoAdminSolutionDetails userDetails);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> UnlockUserAccountAsync(ManagedObjectReference _this, SsoPrincipalId userId);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> EnableUserAccountAsync(ManagedObjectReference _this, SsoPrincipalId userId);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> DisableUserAccountAsync(ManagedObjectReference _this, SsoPrincipalId userId);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<int> GetDaysRemainingUntilPasswordExpirationAsync(ManagedObjectReference _this, SsoPrincipalId userId);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<int> GetDaysRemainingUntilSelfPasswordExpirationAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> RegisterExternalUserAsync(ManagedObjectReference _this, SsoPrincipalId externalUserId);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> RemoveExternalUserAsync(ManagedObjectReference _this, SsoPrincipalId externalUserId);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInternalFault), Action="urn:sso/version3_5", Name="SsoFaultInternalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(NotSupported), Action="urn:sso/version3_5", Name="NotSupportedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<ExportFullStateResponse> ExportFullStateAsync(ExportFullStateRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInternalFault), Action="urn:sso/version3_5", Name="SsoFaultInternalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(InvalidArgument), Action="urn:sso/version3_5", Name="InvalidArgumentFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(NotSupported), Action="urn:sso/version3_5", Name="NotSupportedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<ImportFullStateResponse> ImportFullStateAsync(ImportFullStateRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> SetRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId, string role);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> HasAdministratorRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> HasConfigurationUserRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> HasRegularUserRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> GrantWSTrustRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId, string role);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> RevokeWSTrustRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId, string role);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> GrantIDPProvisioningRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId, string role);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> RevokeIDPProvisioningRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId, string role);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminServiceContent> SsoAdminServiceInstanceAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultSmtpConfigNotSetFault), Action="urn:sso/version3_5", Name="SsoAdminFaultSmtpConfigNotSetFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminSmtpConfig> GetSmtpConfigurationAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task UpdateSmtpConfigurationAsync(ManagedObjectReference _this, SsoAdminSmtpConfig config);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultSmtpConfigNotSetFault), Action="urn:sso/version3_5", Name="SsoAdminFaultSmtpConfigNotSetFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task SendMailAsync(ManagedObjectReference _this, SsoAdminMailContent content);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminSsoHealthStats> GetSsoStatisticsAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoAdminActiveDirectoryJoinInfo> IdS_getActiveDirectoryJoinStatusAsync(ManagedObjectReference _this);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultADDomainAccessDeniedFault), Action="urn:sso/version3_5", Name="SsoAdminFaultADDomainAccessDeniedFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultADDomainAlreadyJoinedFault), Action="urn:sso/version3_5", Name="SsoAdminFaultADDomainAlreadyJoinedFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultADDomainUnknownDomainFault), Action="urn:sso/version3_5", Name="SsoAdminFaultADDomainUnknownDomainFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task JoinActiveDirectoryAsync(ManagedObjectReference _this, string username, string password, string domain, string orgUnit);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultADDomainAccessDeniedFault), Action="urn:sso/version3_5", Name="SsoAdminFaultADDomainAccessDeniedFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultADDomainNotJoinedFault), Action="urn:sso/version3_5", Name="SsoAdminFaultADDomainNotJoinedFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultADDomainUnknownDomainFault), Action="urn:sso/version3_5", Name="SsoAdminFaultADDomainUnknownDomainFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoAdminFaultADIDSAlreadyExistFault), Action="urn:sso/version3_5", Name="SsoAdminFaultADIDSAlreadyExistFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task LeaveActiveDirectoryAsync(ManagedObjectReference _this, string username, string password);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<bool> IsMemberOfGroupAsync(ManagedObjectReference _this, SsoPrincipalId userId, SsoPrincipalId groupId);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindParentGroupsResponse> FindParentGroupsAsync(FindParentGroupsRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidPrincipalFault), Action="urn:sso/version3_5", Name="SsoFaultInvalidPrincipalFaultFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNoPermission), Action="urn:sso/version3_5", Name="SsoFaultNoPermissionFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultNotAuthenticated), Action="urn:sso/version3_5", Name="SsoFaultNotAuthenticatedFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    System.Threading.Tasks.Task<FindAllParentGroupsResponse> FindAllParentGroupsAsync(FindAllParentGroupsRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="urn:sso/version3_5", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(SsoFaultInvalidCredentials), Action="urn:sso/version3_5", Name="SsoFaultInvalidCredentialsFault")]
    [System.ServiceModel.FaultContractAttribute(typeof(RuntimeFault), Action="urn:sso/version3_5", Name="RuntimeFaultFault")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
    [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
    System.Threading.Tasks.Task<SsoGroupcheckServiceContent> SsoGroupcheckServiceInstanceAsync(ManagedObjectReference _this);
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetAllCertificates", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetAllCertificatesRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    public GetAllCertificatesRequest()
    {
    }

    public GetAllCertificatesRequest(ManagedObjectReference _this)
    {
        this._this = _this;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetAllCertificatesResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetAllCertificatesResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public string[] returnval;

    public GetAllCertificatesResponse()
    {
    }

    public GetAllCertificatesResponse(string[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetComputers", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetComputersRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public bool getDCOnly;

    public GetComputersRequest()
    {
    }

    public GetComputersRequest(ManagedObjectReference _this, bool getDCOnly)
    {
        this._this = _this;
        this.getDCOnly = getDCOnly;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetComputersResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetComputersResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminVmHost[] returnval;

    public GetComputersResponse()
    {
    }

    public GetComputersResponse(SsoAdminVmHost[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetKnownCertificateChains", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetKnownCertificateChainsRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    public GetKnownCertificateChainsRequest()
    {
    }

    public GetKnownCertificateChainsRequest(ManagedObjectReference _this)
    {
        this._this = _this;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetKnownCertificateChainsResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetKnownCertificateChainsResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminConfigurationManagementServiceCertificateChain[] returnval;

    public GetKnownCertificateChainsResponse()
    {
    }

    public GetKnownCertificateChainsResponse(SsoAdminConfigurationManagementServiceCertificateChain[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetTrustedCertificates", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetTrustedCertificatesRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    public GetTrustedCertificatesRequest()
    {
    }

    public GetTrustedCertificatesRequest(ManagedObjectReference _this)
    {
        this._this = _this;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetTrustedCertificatesResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetTrustedCertificatesResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public string[] returnval;

    public GetTrustedCertificatesResponse()
    {
    }

    public GetTrustedCertificatesResponse(string[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetIssuersCertificates", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetIssuersCertificatesRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    public GetIssuersCertificatesRequest()
    {
    }

    public GetIssuersCertificatesRequest(ManagedObjectReference _this)
    {
        this._this = _this;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetIssuersCertificatesResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetIssuersCertificatesResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public string[] returnval;

    public GetIssuersCertificatesResponse()
    {
    }

    public GetIssuersCertificatesResponse(string[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="EnumerateExternalIDPEntityIDs", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class EnumerateExternalIDPEntityIDsRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    public EnumerateExternalIDPEntityIDsRequest()
    {
    }

    public EnumerateExternalIDPEntityIDsRequest(ManagedObjectReference _this)
    {
        this._this = _this;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="EnumerateExternalIDPEntityIDsResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class EnumerateExternalIDPEntityIDsResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public string[] returnval;

    public EnumerateExternalIDPEntityIDsResponse()
    {
    }

    public EnumerateExternalIDPEntityIDsResponse(string[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetExternalIdpTrustedCertificateChains", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetExternalIdpTrustedCertificateChainsRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    public GetExternalIdpTrustedCertificateChainsRequest()
    {
    }

    public GetExternalIdpTrustedCertificateChainsRequest(ManagedObjectReference _this)
    {
        this._this = _this;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetExternalIdpTrustedCertificateChainsResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetExternalIdpTrustedCertificateChainsResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminConfigurationManagementServiceCertificateChain[] returnval;

    public GetExternalIdpTrustedCertificateChainsResponse()
    {
    }

    public GetExternalIdpTrustedCertificateChainsResponse(SsoAdminConfigurationManagementServiceCertificateChain[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="RetrieveHaBackupConfigurationPackage", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class RetrieveHaBackupConfigurationPackageRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    public RetrieveHaBackupConfigurationPackageRequest()
    {
    }

    public RetrieveHaBackupConfigurationPackageRequest(ManagedObjectReference _this)
    {
        this._this = _this;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="RetrieveHaBackupConfigurationPackageResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class RetrieveHaBackupConfigurationPackageResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
    public byte[] returnval;

    public RetrieveHaBackupConfigurationPackageResponse()
    {
    }

    public RetrieveHaBackupConfigurationPackageResponse(byte[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="RetrieveReplicaConfigurationPackage", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class RetrieveReplicaConfigurationPackageRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    public RetrieveReplicaConfigurationPackageRequest()
    {
    }

    public RetrieveReplicaConfigurationPackageRequest(ManagedObjectReference _this)
    {
        this._this = _this;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="RetrieveReplicaConfigurationPackageResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class RetrieveReplicaConfigurationPackageResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
    public byte[] returnval;

    public RetrieveReplicaConfigurationPackageResponse()
    {
    }

    public RetrieveReplicaConfigurationPackageResponse(byte[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="ProbeConnectivity", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class ProbeConnectivityRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI")]
    public string serviceUri;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public string authenticationType;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=3)]
    public SsoAdminDomainManagementServiceAuthenticationCredentails authnCredentials;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=4)]
    [System.Xml.Serialization.XmlElementAttribute("certificates")]
    public string[] certificates;

    public ProbeConnectivityRequest()
    {
    }

    public ProbeConnectivityRequest(ManagedObjectReference _this, string serviceUri, string authenticationType, SsoAdminDomainManagementServiceAuthenticationCredentails authnCredentials, string[] certificates)
    {
        this._this = _this;
        this.serviceUri = serviceUri;
        this.authenticationType = authenticationType;
        this.authnCredentials = authnCredentials;
        this.certificates = certificates;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="ProbeConnectivityResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class ProbeConnectivityResponse
{

    public ProbeConnectivityResponse()
    {
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetUpnSuffixes", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetUpnSuffixesRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public string domainName;

    public GetUpnSuffixesRequest()
    {
    }

    public GetUpnSuffixesRequest(ManagedObjectReference _this, string domainName)
    {
        this._this = _this;
        this.domainName = domainName;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetUpnSuffixesResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetUpnSuffixesResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public string[] returnval;

    public GetUpnSuffixesResponse()
    {
    }

    public GetUpnSuffixesResponse(string[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="SetDefaultDomains", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class SetDefaultDomainsRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    [System.Xml.Serialization.XmlElementAttribute("domainNames")]
    public string[] domainNames;

    public SetDefaultDomainsRequest()
    {
    }

    public SetDefaultDomainsRequest(ManagedObjectReference _this, string[] domainNames)
    {
        this._this = _this;
        this.domainNames = domainNames;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="SetDefaultDomainsResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class SetDefaultDomainsResponse
{

    public SetDefaultDomainsResponse()
    {
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetDefaultDomains", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetDefaultDomainsRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    public GetDefaultDomainsRequest()
    {
    }

    public GetDefaultDomainsRequest(ManagedObjectReference _this)
    {
        this._this = _this;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetDefaultDomainsResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetDefaultDomainsResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public string[] returnval;

    public GetDefaultDomainsResponse()
    {
    }

    public GetDefaultDomainsResponse(string[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="IdS_setDefaultDomains", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class IdS_setDefaultDomainsRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    [System.Xml.Serialization.XmlElementAttribute("domainNames")]
    public string[] domainNames;

    public IdS_setDefaultDomainsRequest()
    {
    }

    public IdS_setDefaultDomainsRequest(ManagedObjectReference _this, string[] domainNames)
    {
        this._this = _this;
        this.domainNames = domainNames;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="IdS_setDefaultDomainsResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class IdS_setDefaultDomainsResponse
{

    public IdS_setDefaultDomainsResponse()
    {
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="IdS_getDefaultDomains", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class IdS_getDefaultDomainsRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    public IdS_getDefaultDomainsRequest()
    {
    }

    public IdS_getDefaultDomainsRequest(ManagedObjectReference _this)
    {
        this._this = _this;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="IdS_getDefaultDomainsResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class IdS_getDefaultDomainsResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public string[] returnval;

    public IdS_getDefaultDomainsResponse()
    {
    }

    public IdS_getDefaultDomainsResponse(string[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="IdS_probeConnectivity", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class IdS_probeConnectivityRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI")]
    public string serviceUri;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public string authenticationType;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=3)]
    public SsoAdminIdentitySourceManagementServiceAuthenticationCredentials authnCredentials;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=4)]
    [System.Xml.Serialization.XmlElementAttribute("certificates")]
    public string[] certificates;

    public IdS_probeConnectivityRequest()
    {
    }

    public IdS_probeConnectivityRequest(ManagedObjectReference _this, string serviceUri, string authenticationType, SsoAdminIdentitySourceManagementServiceAuthenticationCredentials authnCredentials, string[] certificates)
    {
        this._this = _this;
        this.serviceUri = serviceUri;
        this.authenticationType = authenticationType;
        this.authnCredentials = authnCredentials;
        this.certificates = certificates;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="IdS_probeConnectivityResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class IdS_probeConnectivityResponse
{

    public IdS_probeConnectivityResponse()
    {
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindPersonUsers", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindPersonUsersRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public int limit;

    public FindPersonUsersRequest()
    {
    }

    public FindPersonUsersRequest(ManagedObjectReference _this, SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria, int limit)
    {
        this._this = _this;
        this.criteria = criteria;
        this.limit = limit;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindPersonUsersResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindPersonUsersResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminPersonUser[] returnval;

    public FindPersonUsersResponse()
    {
    }

    public FindPersonUsersResponse(SsoAdminPersonUser[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindPersonUsersByName", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindPersonUsersByNameRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public int limit;

    public FindPersonUsersByNameRequest()
    {
    }

    public FindPersonUsersByNameRequest(ManagedObjectReference _this, SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria, int limit)
    {
        this._this = _this;
        this.criteria = criteria;
        this.limit = limit;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindPersonUsersByNameResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindPersonUsersByNameResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminPersonUser[] returnval;

    public FindPersonUsersByNameResponse()
    {
    }

    public FindPersonUsersByNameResponse(SsoAdminPersonUser[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindSolutionUsers", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindSolutionUsersRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public string searchString;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public int limit;

    public FindSolutionUsersRequest()
    {
    }

    public FindSolutionUsersRequest(ManagedObjectReference _this, string searchString, int limit)
    {
        this._this = _this;
        this.searchString = searchString;
        this.limit = limit;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindSolutionUsersResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindSolutionUsersResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminSolutionUser[] returnval;

    public FindSolutionUsersResponse()
    {
    }

    public FindSolutionUsersResponse(SsoAdminSolutionUser[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindUsers", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindUsersRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public int limit;

    public FindUsersRequest()
    {
    }

    public FindUsersRequest(ManagedObjectReference _this, SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria, int limit)
    {
        this._this = _this;
        this.criteria = criteria;
        this.limit = limit;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindUsersResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindUsersResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminUser[] returnval;

    public FindUsersResponse()
    {
    }

    public FindUsersResponse(SsoAdminUser[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindGroups", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindGroupsRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public int limit;

    public FindGroupsRequest()
    {
    }

    public FindGroupsRequest(ManagedObjectReference _this, SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria, int limit)
    {
        this._this = _this;
        this.criteria = criteria;
        this.limit = limit;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindGroupsResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindGroupsResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminGroup[] returnval;

    public FindGroupsResponse()
    {
    }

    public FindGroupsResponse(SsoAdminGroup[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindGroupsByName", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindGroupsByNameRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public int limit;

    public FindGroupsByNameRequest()
    {
    }

    public FindGroupsByNameRequest(ManagedObjectReference _this, SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria, int limit)
    {
        this._this = _this;
        this.criteria = criteria;
        this.limit = limit;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindGroupsByNameResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindGroupsByNameResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminGroup[] returnval;

    public FindGroupsByNameResponse()
    {
    }

    public FindGroupsByNameResponse(SsoAdminGroup[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindUsersInGroup", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindUsersInGroupRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public SsoPrincipalId groupId;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public string searchString;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=3)]
    public int limit;

    public FindUsersInGroupRequest()
    {
    }

    public FindUsersInGroupRequest(ManagedObjectReference _this, SsoPrincipalId groupId, string searchString, int limit)
    {
        this._this = _this;
        this.groupId = groupId;
        this.searchString = searchString;
        this.limit = limit;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindUsersInGroupResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindUsersInGroupResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminUser[] returnval;

    public FindUsersInGroupResponse()
    {
    }

    public FindUsersInGroupResponse(SsoAdminUser[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindPersonUsersByNameInGroup", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindPersonUsersByNameInGroupRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public SsoPrincipalId groupId;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public string searchString;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=3)]
    public int limit;

    public FindPersonUsersByNameInGroupRequest()
    {
    }

    public FindPersonUsersByNameInGroupRequest(ManagedObjectReference _this, SsoPrincipalId groupId, string searchString, int limit)
    {
        this._this = _this;
        this.groupId = groupId;
        this.searchString = searchString;
        this.limit = limit;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindPersonUsersByNameInGroupResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindPersonUsersByNameInGroupResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminPersonUser[] returnval;

    public FindPersonUsersByNameInGroupResponse()
    {
    }

    public FindPersonUsersByNameInGroupResponse(SsoAdminPersonUser[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindPersonUsersInGroup", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindPersonUsersInGroupRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public SsoPrincipalId groupId;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public string searchString;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=3)]
    public int limit;

    public FindPersonUsersInGroupRequest()
    {
    }

    public FindPersonUsersInGroupRequest(ManagedObjectReference _this, SsoPrincipalId groupId, string searchString, int limit)
    {
        this._this = _this;
        this.groupId = groupId;
        this.searchString = searchString;
        this.limit = limit;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindPersonUsersInGroupResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindPersonUsersInGroupResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminPersonUser[] returnval;

    public FindPersonUsersInGroupResponse()
    {
    }

    public FindPersonUsersInGroupResponse(SsoAdminPersonUser[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindSolutionUsersInGroup", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindSolutionUsersInGroupRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public string groupName;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public string searchString;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=3)]
    public int limit;

    public FindSolutionUsersInGroupRequest()
    {
    }

    public FindSolutionUsersInGroupRequest(ManagedObjectReference _this, string groupName, string searchString, int limit)
    {
        this._this = _this;
        this.groupName = groupName;
        this.searchString = searchString;
        this.limit = limit;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindSolutionUsersInGroupResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindSolutionUsersInGroupResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminSolutionUser[] returnval;

    public FindSolutionUsersInGroupResponse()
    {
    }

    public FindSolutionUsersInGroupResponse(SsoAdminSolutionUser[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindGroupsInGroup", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindGroupsInGroupRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public SsoPrincipalId groupId;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public string searchString;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=3)]
    public int limit;

    public FindGroupsInGroupRequest()
    {
    }

    public FindGroupsInGroupRequest(ManagedObjectReference _this, SsoPrincipalId groupId, string searchString, int limit)
    {
        this._this = _this;
        this.groupId = groupId;
        this.searchString = searchString;
        this.limit = limit;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindGroupsInGroupResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindGroupsInGroupResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminGroup[] returnval;

    public FindGroupsInGroupResponse()
    {
    }

    public FindGroupsInGroupResponse(SsoAdminGroup[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindGroupsByNameInGroup", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindGroupsByNameInGroupRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public SsoPrincipalId groupId;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public string searchString;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=3)]
    public int limit;

    public FindGroupsByNameInGroupRequest()
    {
    }

    public FindGroupsByNameInGroupRequest(ManagedObjectReference _this, SsoPrincipalId groupId, string searchString, int limit)
    {
        this._this = _this;
        this.groupId = groupId;
        this.searchString = searchString;
        this.limit = limit;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindGroupsByNameInGroupResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindGroupsByNameInGroupResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminGroup[] returnval;

    public FindGroupsByNameInGroupResponse()
    {
    }

    public FindGroupsByNameInGroupResponse(SsoAdminGroup[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindDirectParentGroups", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindDirectParentGroupsRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public SsoPrincipalId principalId;

    public FindDirectParentGroupsRequest()
    {
    }

    public FindDirectParentGroupsRequest(ManagedObjectReference _this, SsoPrincipalId principalId)
    {
        this._this = _this;
        this.principalId = principalId;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindDirectParentGroupsResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindDirectParentGroupsResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminGroup[] returnval;

    public FindDirectParentGroupsResponse()
    {
    }

    public FindDirectParentGroupsResponse(SsoAdminGroup[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindNestedParentGroups", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindNestedParentGroupsRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public SsoPrincipalId userId;

    public FindNestedParentGroupsRequest()
    {
    }

    public FindNestedParentGroupsRequest(ManagedObjectReference _this, SsoPrincipalId userId)
    {
        this._this = _this;
        this.userId = userId;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindNestedParentGroupsResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindNestedParentGroupsResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminGroup[] returnval;

    public FindNestedParentGroupsResponse()
    {
    }

    public FindNestedParentGroupsResponse(SsoAdminGroup[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindLockedUsers", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindLockedUsersRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public string searchString;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public int limit;

    public FindLockedUsersRequest()
    {
    }

    public FindLockedUsersRequest(ManagedObjectReference _this, string searchString, int limit)
    {
        this._this = _this;
        this.searchString = searchString;
        this.limit = limit;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindLockedUsersResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindLockedUsersResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminPersonUser[] returnval;

    public FindLockedUsersResponse()
    {
    }

    public FindLockedUsersResponse(SsoAdminPersonUser[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindDisabledPersonUsers", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindDisabledPersonUsersRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public string searchString;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public int limit;

    public FindDisabledPersonUsersRequest()
    {
    }

    public FindDisabledPersonUsersRequest(ManagedObjectReference _this, string searchString, int limit)
    {
        this._this = _this;
        this.searchString = searchString;
        this.limit = limit;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindDisabledPersonUsersResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindDisabledPersonUsersResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminPersonUser[] returnval;

    public FindDisabledPersonUsersResponse()
    {
    }

    public FindDisabledPersonUsersResponse(SsoAdminPersonUser[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindDisabledSolutionUsers", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindDisabledSolutionUsersRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public string searchString;

    public FindDisabledSolutionUsersRequest()
    {
    }

    public FindDisabledSolutionUsersRequest(ManagedObjectReference _this, string searchString)
    {
        this._this = _this;
        this.searchString = searchString;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindDisabledSolutionUsersResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindDisabledSolutionUsersResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoAdminSolutionUser[] returnval;

    public FindDisabledSolutionUsersResponse()
    {
    }

    public FindDisabledSolutionUsersResponse(SsoAdminSolutionUser[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetImplicitGroupNames", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetImplicitGroupNamesRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    public GetImplicitGroupNamesRequest()
    {
    }

    public GetImplicitGroupNamesRequest(ManagedObjectReference _this)
    {
        this._this = _this;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetImplicitGroupNamesResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class GetImplicitGroupNamesResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public string[] returnval;

    public GetImplicitGroupNamesResponse()
    {
    }

    public GetImplicitGroupNamesResponse(string[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="RemovePrincipalsFromLocalGroup", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class RemovePrincipalsFromLocalGroupRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    [System.Xml.Serialization.XmlElementAttribute("principalsIds")]
    public SsoPrincipalId[] principalsIds;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public string groupName;

    public RemovePrincipalsFromLocalGroupRequest()
    {
    }

    public RemovePrincipalsFromLocalGroupRequest(ManagedObjectReference _this, SsoPrincipalId[] principalsIds, string groupName)
    {
        this._this = _this;
        this.principalsIds = principalsIds;
        this.groupName = groupName;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="RemovePrincipalsFromLocalGroupResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class RemovePrincipalsFromLocalGroupResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public bool[] returnval;

    public RemovePrincipalsFromLocalGroupResponse()
    {
    }

    public RemovePrincipalsFromLocalGroupResponse(bool[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="AddUsersToLocalGroup", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class AddUsersToLocalGroupRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    [System.Xml.Serialization.XmlElementAttribute("userIds")]
    public SsoPrincipalId[] userIds;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public string groupName;

    public AddUsersToLocalGroupRequest()
    {
    }

    public AddUsersToLocalGroupRequest(ManagedObjectReference _this, SsoPrincipalId[] userIds, string groupName)
    {
        this._this = _this;
        this.userIds = userIds;
        this.groupName = groupName;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="AddUsersToLocalGroupResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class AddUsersToLocalGroupResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public bool[] returnval;

    public AddUsersToLocalGroupResponse()
    {
    }

    public AddUsersToLocalGroupResponse(bool[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="AddGroupsToLocalGroup", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class AddGroupsToLocalGroupRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    [System.Xml.Serialization.XmlElementAttribute("groupIds")]
    public SsoPrincipalId[] groupIds;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    public string groupName;

    public AddGroupsToLocalGroupRequest()
    {
    }

    public AddGroupsToLocalGroupRequest(ManagedObjectReference _this, SsoPrincipalId[] groupIds, string groupName)
    {
        this._this = _this;
        this.groupIds = groupIds;
        this.groupName = groupName;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="AddGroupsToLocalGroupResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class AddGroupsToLocalGroupResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public bool[] returnval;

    public AddGroupsToLocalGroupResponse()
    {
    }

    public AddGroupsToLocalGroupResponse(bool[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="ExportFullState", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class ExportFullStateRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    public ExportFullStateRequest()
    {
    }

    public ExportFullStateRequest(ManagedObjectReference _this)
    {
        this._this = _this;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="ExportFullStateResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class ExportFullStateResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
    public byte[] returnval;

    public ExportFullStateResponse()
    {
    }

    public ExportFullStateResponse(byte[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="ImportFullState", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class ImportFullStateRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
    public byte[] fullState;

    public ImportFullStateRequest()
    {
    }

    public ImportFullStateRequest(ManagedObjectReference _this, byte[] fullState)
    {
        this._this = _this;
        this.fullState = fullState;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="ImportFullStateResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class ImportFullStateResponse
{

    public ImportFullStateResponse()
    {
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindParentGroups", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindParentGroupsRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public SsoPrincipalId userId;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=2)]
    [System.Xml.Serialization.XmlElementAttribute("groupList")]
    public SsoPrincipalId[] groupList;

    public FindParentGroupsRequest()
    {
    }

    public FindParentGroupsRequest(ManagedObjectReference _this, SsoPrincipalId userId, SsoPrincipalId[] groupList)
    {
        this._this = _this;
        this.userId = userId;
        this.groupList = groupList;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindParentGroupsResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindParentGroupsResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoPrincipalId[] returnval;

    public FindParentGroupsResponse()
    {
    }

    public FindParentGroupsResponse(SsoPrincipalId[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindAllParentGroups", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindAllParentGroupsRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    public ManagedObjectReference _this;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=1)]
    public SsoPrincipalId userId;

    public FindAllParentGroupsRequest()
    {
    }

    public FindAllParentGroupsRequest(ManagedObjectReference _this, SsoPrincipalId userId)
    {
        this._this = _this;
        this.userId = userId;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="FindAllParentGroupsResponse", WrapperNamespace="urn:sso", IsWrapped=true)]
public partial class FindAllParentGroupsResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:sso", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("returnval")]
    public SsoPrincipalId[] returnval;

    public FindAllParentGroupsResponse()
    {
    }

    public FindAllParentGroupsResponse(SsoPrincipalId[] returnval)
    {
        this.returnval = returnval;
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
public interface SsoPortTypeChannel : SsoPortType, System.ServiceModel.IClientChannel
{
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.2")]
public partial class SsoPortTypeClient : System.ServiceModel.ClientBase<SsoPortType>, SsoPortType
{

    /// <summary>
    /// Implement this partial method to configure the service endpoint.
    /// </summary>
    /// <param name="serviceEndpoint">The endpoint to configure</param>
    /// <param name="clientCredentials">The client credentials</param>
    static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);

    public SsoPortTypeClient() :
            base(SsoPortTypeClient.GetDefaultBinding(), SsoPortTypeClient.GetDefaultEndpointAddress())
    {
        this.Endpoint.Name = EndpointConfiguration.SsoPort.ToString();
        ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
    }

    public SsoPortTypeClient(EndpointConfiguration endpointConfiguration) :
            base(SsoPortTypeClient.GetBindingForEndpoint(endpointConfiguration), SsoPortTypeClient.GetEndpointAddress(endpointConfiguration))
    {
        this.Endpoint.Name = endpointConfiguration.ToString();
        ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
    }

    public SsoPortTypeClient(EndpointConfiguration endpointConfiguration, string remoteAddress) :
            base(SsoPortTypeClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
    {
        this.Endpoint.Name = endpointConfiguration.ToString();
        ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
    }

    public SsoPortTypeClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) :
            base(SsoPortTypeClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
    {
        this.Endpoint.Name = endpointConfiguration.ToString();
        ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
    }

    public SsoPortTypeClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) :
            base(binding, remoteAddress)
    {
    }

    public System.Threading.Tasks.Task LoginAsync(ManagedObjectReference _this)
    {
        return base.Channel.LoginAsync(_this);
    }

    public System.Threading.Tasks.Task LogoutAsync(ManagedObjectReference _this)
    {
        return base.Channel.LogoutAsync(_this);
    }

    public System.Threading.Tasks.Task<string> SetLocaleAsync(ManagedObjectReference _this, string locale)
    {
        return base.Channel.SetLocaleAsync(_this, locale);
    }

    public System.Threading.Tasks.Task<string> GetLocaleAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetLocaleAsync(_this);
    }

    public System.Threading.Tasks.Task<bool> AddCertificateAsync(ManagedObjectReference _this, string certificate)
    {
        return base.Channel.AddCertificateAsync(_this, certificate);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<GetAllCertificatesResponse> SsoPortType.GetAllCertificatesAsync(GetAllCertificatesRequest request)
    {
        return base.Channel.GetAllCertificatesAsync(request);
    }

    public System.Threading.Tasks.Task<GetAllCertificatesResponse> GetAllCertificatesAsync(ManagedObjectReference _this)
    {
        GetAllCertificatesRequest inValue = new GetAllCertificatesRequest();
        inValue._this = _this;
        return ((SsoPortType)(this)).GetAllCertificatesAsync(inValue);
    }

    public System.Threading.Tasks.Task<string> FindCertificateAsync(ManagedObjectReference _this, string fingerprint)
    {
        return base.Channel.FindCertificateAsync(_this, fingerprint);
    }

    public System.Threading.Tasks.Task<bool> DeleteCertificateAsync(ManagedObjectReference _this, string fingerprint)
    {
        return base.Channel.DeleteCertificateAsync(_this, fingerprint);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<GetComputersResponse> SsoPortType.GetComputersAsync(GetComputersRequest request)
    {
        return base.Channel.GetComputersAsync(request);
    }

    public System.Threading.Tasks.Task<GetComputersResponse> GetComputersAsync(ManagedObjectReference _this, bool getDCOnly)
    {
        GetComputersRequest inValue = new GetComputersRequest();
        inValue._this = _this;
        inValue.getDCOnly = getDCOnly;
        return ((SsoPortType)(this)).GetComputersAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<GetKnownCertificateChainsResponse> SsoPortType.GetKnownCertificateChainsAsync(GetKnownCertificateChainsRequest request)
    {
        return base.Channel.GetKnownCertificateChainsAsync(request);
    }

    public System.Threading.Tasks.Task<GetKnownCertificateChainsResponse> GetKnownCertificateChainsAsync(ManagedObjectReference _this)
    {
        GetKnownCertificateChainsRequest inValue = new GetKnownCertificateChainsRequest();
        inValue._this = _this;
        return ((SsoPortType)(this)).GetKnownCertificateChainsAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<GetTrustedCertificatesResponse> SsoPortType.GetTrustedCertificatesAsync(GetTrustedCertificatesRequest request)
    {
        return base.Channel.GetTrustedCertificatesAsync(request);
    }

    public System.Threading.Tasks.Task<GetTrustedCertificatesResponse> GetTrustedCertificatesAsync(ManagedObjectReference _this)
    {
        GetTrustedCertificatesRequest inValue = new GetTrustedCertificatesRequest();
        inValue._this = _this;
        return ((SsoPortType)(this)).GetTrustedCertificatesAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<GetIssuersCertificatesResponse> SsoPortType.GetIssuersCertificatesAsync(GetIssuersCertificatesRequest request)
    {
        return base.Channel.GetIssuersCertificatesAsync(request);
    }

    public System.Threading.Tasks.Task<GetIssuersCertificatesResponse> GetIssuersCertificatesAsync(ManagedObjectReference _this)
    {
        GetIssuersCertificatesRequest inValue = new GetIssuersCertificatesRequest();
        inValue._this = _this;
        return ((SsoPortType)(this)).GetIssuersCertificatesAsync(inValue);
    }

    public System.Threading.Tasks.Task ImportTrustedSTSConfigurationAsync(ManagedObjectReference _this, SsoAdminTrustedSTSConfig stsConfig)
    {
        return base.Channel.ImportTrustedSTSConfigurationAsync(_this, stsConfig);
    }

    public System.Threading.Tasks.Task RemoveTrustedSTSConfigurationAsync(ManagedObjectReference _this, string issuerName)
    {
        return base.Channel.RemoveTrustedSTSConfigurationAsync(_this, issuerName);
    }

    public System.Threading.Tasks.Task ImportExternalIDPConfigurationAsync(ManagedObjectReference _this, string externalIDPConfigDoc)
    {
        return base.Channel.ImportExternalIDPConfigurationAsync(_this, externalIDPConfigDoc);
    }

    public System.Threading.Tasks.Task CreateExternalIDPConfigurationAsync(ManagedObjectReference _this, SsoAdminIDPConfiguration config)
    {
        return base.Channel.CreateExternalIDPConfigurationAsync(_this, config);
    }

    public System.Threading.Tasks.Task<SsoAdminIDPConfiguration> GetExternalIDPConfigurationAsync(ManagedObjectReference _this, string entityID)
    {
        return base.Channel.GetExternalIDPConfigurationAsync(_this, entityID);
    }

    public System.Threading.Tasks.Task SetExternalIDPConfigurationAsync(ManagedObjectReference _this, SsoAdminIDPConfiguration config)
    {
        return base.Channel.SetExternalIDPConfigurationAsync(_this, config);
    }

    public System.Threading.Tasks.Task DeleteExternalIDPConfigurationAsync(ManagedObjectReference _this, string entityID)
    {
        return base.Channel.DeleteExternalIDPConfigurationAsync(_this, entityID);
    }

    public System.Threading.Tasks.Task DeleteExternalIDPConfigurationAndUsersAsync(ManagedObjectReference _this, string entityID)
    {
        return base.Channel.DeleteExternalIDPConfigurationAndUsersAsync(_this, entityID);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<EnumerateExternalIDPEntityIDsResponse> SsoPortType.EnumerateExternalIDPEntityIDsAsync(EnumerateExternalIDPEntityIDsRequest request)
    {
        return base.Channel.EnumerateExternalIDPEntityIDsAsync(request);
    }

    public System.Threading.Tasks.Task<EnumerateExternalIDPEntityIDsResponse> EnumerateExternalIDPEntityIDsAsync(ManagedObjectReference _this)
    {
        EnumerateExternalIDPEntityIDsRequest inValue = new EnumerateExternalIDPEntityIDsRequest();
        inValue._this = _this;
        return ((SsoPortType)(this)).EnumerateExternalIDPEntityIDsAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<GetExternalIdpTrustedCertificateChainsResponse> SsoPortType.GetExternalIdpTrustedCertificateChainsAsync(GetExternalIdpTrustedCertificateChainsRequest request)
    {
        return base.Channel.GetExternalIdpTrustedCertificateChainsAsync(request);
    }

    public System.Threading.Tasks.Task<GetExternalIdpTrustedCertificateChainsResponse> GetExternalIdpTrustedCertificateChainsAsync(ManagedObjectReference _this)
    {
        GetExternalIdpTrustedCertificateChainsRequest inValue = new GetExternalIdpTrustedCertificateChainsRequest();
        inValue._this = _this;
        return ((SsoPortType)(this)).GetExternalIdpTrustedCertificateChainsAsync(inValue);
    }

    public System.Threading.Tasks.Task<SsoAdminConfigurationManagementServiceCertificateChain> GetExternalIdpTrustedCertificateChainAsync(ManagedObjectReference _this, string entityId)
    {
        return base.Channel.GetExternalIdpTrustedCertificateChainAsync(_this, entityId);
    }

    public System.Threading.Tasks.Task<bool> DeleteTrustedCertificateAsync(ManagedObjectReference _this, string fingerprint)
    {
        return base.Channel.DeleteTrustedCertificateAsync(_this, fingerprint);
    }

    public System.Threading.Tasks.Task SetNewSignerIdentityAsync(ManagedObjectReference _this, string signingKey, SsoAdminConfigurationManagementServiceCertificateChain signingCertificateChain)
    {
        return base.Channel.SetNewSignerIdentityAsync(_this, signingKey, signingCertificateChain);
    }

    public System.Threading.Tasks.Task SetSignerIdentityAsync(ManagedObjectReference _this, SsoPrincipalId adminUser, string adminPass, string signingKey, SsoAdminConfigurationManagementServiceCertificateChain signingCertificateChain)
    {
        return base.Channel.SetSignerIdentityAsync(_this, adminUser, adminPass, signingKey, signingCertificateChain);
    }

    public System.Threading.Tasks.Task<long> GetClockToleranceAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetClockToleranceAsync(_this);
    }

    public System.Threading.Tasks.Task SetClockToleranceAsync(ManagedObjectReference _this, long milliseconds)
    {
        return base.Channel.SetClockToleranceAsync(_this, milliseconds);
    }

    public System.Threading.Tasks.Task<int> GetDelegationCountAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetDelegationCountAsync(_this);
    }

    public System.Threading.Tasks.Task SetDelegationCountAsync(ManagedObjectReference _this, int delegationCount)
    {
        return base.Channel.SetDelegationCountAsync(_this, delegationCount);
    }

    public System.Threading.Tasks.Task<int> GetRenewCountAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetRenewCountAsync(_this);
    }

    public System.Threading.Tasks.Task SetRenewCountAsync(ManagedObjectReference _this, int renewCount)
    {
        return base.Channel.SetRenewCountAsync(_this, renewCount);
    }

    public System.Threading.Tasks.Task<long> GetMaximumBearerTokenLifetimeAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetMaximumBearerTokenLifetimeAsync(_this);
    }

    public System.Threading.Tasks.Task SetMaximumBearerTokenLifetimeAsync(ManagedObjectReference _this, long maxLifetime)
    {
        return base.Channel.SetMaximumBearerTokenLifetimeAsync(_this, maxLifetime);
    }

    public System.Threading.Tasks.Task<long> GetMaximumHoKTokenLifetimeAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetMaximumHoKTokenLifetimeAsync(_this);
    }

    public System.Threading.Tasks.Task SetMaximumHoKTokenLifetimeAsync(ManagedObjectReference _this, long maxLifetime)
    {
        return base.Channel.SetMaximumHoKTokenLifetimeAsync(_this, maxLifetime);
    }

    public System.Threading.Tasks.Task<SsoAdminPasswordExpirationConfig> GetPasswordExpirationConfigurationAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetPasswordExpirationConfigurationAsync(_this);
    }

    public System.Threading.Tasks.Task UpdatePasswordExpirationConfigurationAsync(ManagedObjectReference _this, SsoAdminPasswordExpirationConfig config)
    {
        return base.Channel.UpdatePasswordExpirationConfigurationAsync(_this, config);
    }

    public System.Threading.Tasks.Task ImportSAMLMetadataAsync(ManagedObjectReference _this, string samlConfigDoc)
    {
        return base.Channel.ImportSAMLMetadataAsync(_this, samlConfigDoc);
    }

    public System.Threading.Tasks.Task DeleteRelyingPartyAsync(ManagedObjectReference _this, string rpName)
    {
        return base.Channel.DeleteRelyingPartyAsync(_this, rpName);
    }

    public System.Threading.Tasks.Task<string> GetIssuerNameAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetIssuerNameAsync(_this);
    }

    public System.Threading.Tasks.Task<bool> IsExternalIDPJitEnabledAsync(ManagedObjectReference _this, string entityID)
    {
        return base.Channel.IsExternalIDPJitEnabledAsync(_this, entityID);
    }

    public System.Threading.Tasks.Task SetExternalIDPJitAttributeAsync(ManagedObjectReference _this, string entityID, bool enableJit)
    {
        return base.Channel.SetExternalIDPJitAttributeAsync(_this, entityID, enableJit);
    }

    public System.Threading.Tasks.Task SetAuthnPolicyAsync(ManagedObjectReference _this, SsoAdminAuthnPolicy policy)
    {
        return base.Channel.SetAuthnPolicyAsync(_this, policy);
    }

    public System.Threading.Tasks.Task<SsoAdminAuthnPolicy> GetAuthnPolicyAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetAuthnPolicyAsync(_this);
    }

    public System.Threading.Tasks.Task<bool> IsMultiSiteDeploymentAsync(ManagedObjectReference _this)
    {
        return base.Channel.IsMultiSiteDeploymentAsync(_this);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<RetrieveHaBackupConfigurationPackageResponse> SsoPortType.RetrieveHaBackupConfigurationPackageAsync(RetrieveHaBackupConfigurationPackageRequest request)
    {
        return base.Channel.RetrieveHaBackupConfigurationPackageAsync(request);
    }

    public System.Threading.Tasks.Task<RetrieveHaBackupConfigurationPackageResponse> RetrieveHaBackupConfigurationPackageAsync(ManagedObjectReference _this)
    {
        RetrieveHaBackupConfigurationPackageRequest inValue = new RetrieveHaBackupConfigurationPackageRequest();
        inValue._this = _this;
        return ((SsoPortType)(this)).RetrieveHaBackupConfigurationPackageAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<RetrieveReplicaConfigurationPackageResponse> SsoPortType.RetrieveReplicaConfigurationPackageAsync(RetrieveReplicaConfigurationPackageRequest request)
    {
        return base.Channel.RetrieveReplicaConfigurationPackageAsync(request);
    }

    public System.Threading.Tasks.Task<RetrieveReplicaConfigurationPackageResponse> RetrieveReplicaConfigurationPackageAsync(ManagedObjectReference _this)
    {
        RetrieveReplicaConfigurationPackageRequest inValue = new RetrieveReplicaConfigurationPackageRequest();
        inValue._this = _this;
        return ((SsoPortType)(this)).RetrieveReplicaConfigurationPackageAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<ProbeConnectivityResponse> SsoPortType.ProbeConnectivityAsync(ProbeConnectivityRequest request)
    {
        return base.Channel.ProbeConnectivityAsync(request);
    }

    public System.Threading.Tasks.Task<ProbeConnectivityResponse> ProbeConnectivityAsync(ManagedObjectReference _this, string serviceUri, string authenticationType, SsoAdminDomainManagementServiceAuthenticationCredentails authnCredentials, string[] certificates)
    {
        ProbeConnectivityRequest inValue = new ProbeConnectivityRequest();
        inValue._this = _this;
        inValue.serviceUri = serviceUri;
        inValue.authenticationType = authenticationType;
        inValue.authnCredentials = authnCredentials;
        inValue.certificates = certificates;
        return ((SsoPortType)(this)).ProbeConnectivityAsync(inValue);
    }

    public System.Threading.Tasks.Task AddExternalDomainAsync(ManagedObjectReference _this, string serverType, string domainName, string domainAlias, SsoAdminExternalDomainDetails details, string authenticationType, SsoAdminDomainManagementServiceAuthenticationCredentails authnCredentials)
    {
        return base.Channel.AddExternalDomainAsync(_this, serverType, domainName, domainAlias, details, authenticationType, authnCredentials);
    }

    public System.Threading.Tasks.Task RegisterLocalOSDomainAsync(ManagedObjectReference _this, string domainName)
    {
        return base.Channel.RegisterLocalOSDomainAsync(_this, domainName);
    }

    public System.Threading.Tasks.Task<SsoAdminDomains> GetDomainsAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetDomainsAsync(_this);
    }

    public System.Threading.Tasks.Task<SsoAdminExternalDomain> FindExternalDomainAsync(ManagedObjectReference _this, string name)
    {
        return base.Channel.FindExternalDomainAsync(_this, name);
    }

    public System.Threading.Tasks.Task SetBrandNameAsync(ManagedObjectReference _this, string brandName)
    {
        return base.Channel.SetBrandNameAsync(_this, brandName);
    }

    public System.Threading.Tasks.Task<string> GetBrandNameAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetBrandNameAsync(_this);
    }

    public System.Threading.Tasks.Task SetLogonBannerAsync(ManagedObjectReference _this, string logonBanner)
    {
        return base.Channel.SetLogonBannerAsync(_this, logonBanner);
    }

    public System.Threading.Tasks.Task<string> GetLogonBannerAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetLogonBannerAsync(_this);
    }

    public System.Threading.Tasks.Task SetLogonBannerTitleAsync(ManagedObjectReference _this, string logonBannerTitle)
    {
        return base.Channel.SetLogonBannerTitleAsync(_this, logonBannerTitle);
    }

    public System.Threading.Tasks.Task<string> GetLogonBannerTitleAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetLogonBannerTitleAsync(_this);
    }

    public System.Threading.Tasks.Task SetLogonBannerContentAsync(ManagedObjectReference _this, string logonBannerContent)
    {
        return base.Channel.SetLogonBannerContentAsync(_this, logonBannerContent);
    }

    public System.Threading.Tasks.Task<string> GetLogonBannerContentAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetLogonBannerContentAsync(_this);
    }

    public System.Threading.Tasks.Task SetLogonBannerCheckboxFlagAsync(ManagedObjectReference _this, bool enableLogonBannerCheckbox)
    {
        return base.Channel.SetLogonBannerCheckboxFlagAsync(_this, enableLogonBannerCheckbox);
    }

    public System.Threading.Tasks.Task<bool> GetLogonBannerCheckboxFlagAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetLogonBannerCheckboxFlagAsync(_this);
    }

    public System.Threading.Tasks.Task DisableLogonBannerAsync(ManagedObjectReference _this)
    {
        return base.Channel.DisableLogonBannerAsync(_this);
    }

    public System.Threading.Tasks.Task<bool> IsLogonBannerDisabledAsync(ManagedObjectReference _this)
    {
        return base.Channel.IsLogonBannerDisabledAsync(_this);
    }

    public System.Threading.Tasks.Task<string> GetSystemDomainNameAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetSystemDomainNameAsync(_this);
    }

    public System.Threading.Tasks.Task<string> GetSystemTenantNameAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetSystemTenantNameAsync(_this);
    }

    public System.Threading.Tasks.Task<string> GetLocalOSDomainNameAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetLocalOSDomainNameAsync(_this);
    }

    public System.Threading.Tasks.Task UpdateExternalDomainDetailsAsync(ManagedObjectReference _this, string name, SsoAdminExternalDomainDetails details)
    {
        return base.Channel.UpdateExternalDomainDetailsAsync(_this, name, details);
    }

    public System.Threading.Tasks.Task UpdateExternalDomainAuthnTypeAsync(ManagedObjectReference _this, string name, string authnType, SsoAdminDomainManagementServiceAuthenticationCredentails authnCredentials)
    {
        return base.Channel.UpdateExternalDomainAuthnTypeAsync(_this, name, authnType, authnCredentials);
    }

    public System.Threading.Tasks.Task<bool> RegisterUpnSuffixAsync(ManagedObjectReference _this, string domainName, string upnSuffix)
    {
        return base.Channel.RegisterUpnSuffixAsync(_this, domainName, upnSuffix);
    }

    public System.Threading.Tasks.Task<bool> UnRegisterUpnSuffixAsync(ManagedObjectReference _this, string domainName, string upnSuffix)
    {
        return base.Channel.UnRegisterUpnSuffixAsync(_this, domainName, upnSuffix);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<GetUpnSuffixesResponse> SsoPortType.GetUpnSuffixesAsync(GetUpnSuffixesRequest request)
    {
        return base.Channel.GetUpnSuffixesAsync(request);
    }

    public System.Threading.Tasks.Task<GetUpnSuffixesResponse> GetUpnSuffixesAsync(ManagedObjectReference _this, string domainName)
    {
        GetUpnSuffixesRequest inValue = new GetUpnSuffixesRequest();
        inValue._this = _this;
        inValue.domainName = domainName;
        return ((SsoPortType)(this)).GetUpnSuffixesAsync(inValue);
    }

    public System.Threading.Tasks.Task DeleteDomainAsync(ManagedObjectReference _this, string name)
    {
        return base.Channel.DeleteDomainAsync(_this, name);
    }

    public System.Threading.Tasks.Task<ManagedObjectReference> GetSslCertificateManagerAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetSslCertificateManagerAsync(_this);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<SetDefaultDomainsResponse> SsoPortType.SetDefaultDomainsAsync(SetDefaultDomainsRequest request)
    {
        return base.Channel.SetDefaultDomainsAsync(request);
    }

    public System.Threading.Tasks.Task<SetDefaultDomainsResponse> SetDefaultDomainsAsync(ManagedObjectReference _this, string[] domainNames)
    {
        SetDefaultDomainsRequest inValue = new SetDefaultDomainsRequest();
        inValue._this = _this;
        inValue.domainNames = domainNames;
        return ((SsoPortType)(this)).SetDefaultDomainsAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<GetDefaultDomainsResponse> SsoPortType.GetDefaultDomainsAsync(GetDefaultDomainsRequest request)
    {
        return base.Channel.GetDefaultDomainsAsync(request);
    }

    public System.Threading.Tasks.Task<GetDefaultDomainsResponse> GetDefaultDomainsAsync(ManagedObjectReference _this)
    {
        GetDefaultDomainsRequest inValue = new GetDefaultDomainsRequest();
        inValue._this = _this;
        return ((SsoPortType)(this)).GetDefaultDomainsAsync(inValue);
    }

    public System.Threading.Tasks.Task<SsoAdminConfigurationManagementServiceCertificateChain> GetSslIdentityAsync(ManagedObjectReference _this, string host, int ldapsPort)
    {
        return base.Channel.GetSslIdentityAsync(_this, host, ldapsPort);
    }

    public System.Threading.Tasks.Task RegisterLdapAsync(ManagedObjectReference _this, string serverType, string domainName, string domainAlias, SsoAdminLdapIdentitySourceDetails details, string authenticationType, SsoAdminIdentitySourceManagementServiceAuthenticationCredentials authnCredentials)
    {
        return base.Channel.RegisterLdapAsync(_this, serverType, domainName, domainAlias, details, authenticationType, authnCredentials);
    }

    public System.Threading.Tasks.Task RegisterActiveDirectoryAsync(ManagedObjectReference _this, string domainName, SsoAdminIdentitySourceManagementServiceAuthenticationCredentials authnCredentials, SsoAdminExternalDomainSchemaDetails schemaMapping)
    {
        return base.Channel.RegisterActiveDirectoryAsync(_this, domainName, authnCredentials, schemaMapping);
    }

    public System.Threading.Tasks.Task RegisterLocalOSAsync(ManagedObjectReference _this, string name)
    {
        return base.Channel.RegisterLocalOSAsync(_this, name);
    }

    public System.Threading.Tasks.Task<SsoAdminIdentitySources> GetAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetAsync(_this);
    }

    public System.Threading.Tasks.Task<SsoAdminAuthenticationAccountInfo> GetActiveDirectoryAuthnAccountInfoAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetActiveDirectoryAuthnAccountInfoAsync(_this);
    }

    public System.Threading.Tasks.Task<string> IdS_getSystemDomainNameAsync(ManagedObjectReference _this)
    {
        return base.Channel.IdS_getSystemDomainNameAsync(_this);
    }

    public System.Threading.Tasks.Task UpdateLdapAsync(ManagedObjectReference _this, string name, SsoAdminLdapIdentitySourceDetails details)
    {
        return base.Channel.UpdateLdapAsync(_this, name, details);
    }

    public System.Threading.Tasks.Task UpdateActiveDirectoryAsync(ManagedObjectReference _this, string domainName, SsoAdminIdentitySourceManagementServiceAuthenticationCredentials authnCredentials, SsoAdminExternalDomainSchemaDetails schemaMapping)
    {
        return base.Channel.UpdateActiveDirectoryAsync(_this, domainName, authnCredentials, schemaMapping);
    }

    public System.Threading.Tasks.Task UpdateLdapAuthnTypeAsync(ManagedObjectReference _this, string name, string authnType, SsoAdminIdentitySourceManagementServiceAuthenticationCredentials authnCredentials)
    {
        return base.Channel.UpdateLdapAuthnTypeAsync(_this, name, authnType, authnCredentials);
    }

    public System.Threading.Tasks.Task DeleteAsync(ManagedObjectReference _this, string name)
    {
        return base.Channel.DeleteAsync(_this, name);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<IdS_setDefaultDomainsResponse> SsoPortType.IdS_setDefaultDomainsAsync(IdS_setDefaultDomainsRequest request)
    {
        return base.Channel.IdS_setDefaultDomainsAsync(request);
    }

    public System.Threading.Tasks.Task<IdS_setDefaultDomainsResponse> IdS_setDefaultDomainsAsync(ManagedObjectReference _this, string[] domainNames)
    {
        IdS_setDefaultDomainsRequest inValue = new IdS_setDefaultDomainsRequest();
        inValue._this = _this;
        inValue.domainNames = domainNames;
        return ((SsoPortType)(this)).IdS_setDefaultDomainsAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<IdS_getDefaultDomainsResponse> SsoPortType.IdS_getDefaultDomainsAsync(IdS_getDefaultDomainsRequest request)
    {
        return base.Channel.IdS_getDefaultDomainsAsync(request);
    }

    public System.Threading.Tasks.Task<IdS_getDefaultDomainsResponse> IdS_getDefaultDomainsAsync(ManagedObjectReference _this)
    {
        IdS_getDefaultDomainsRequest inValue = new IdS_getDefaultDomainsRequest();
        inValue._this = _this;
        return ((SsoPortType)(this)).IdS_getDefaultDomainsAsync(inValue);
    }

    public System.Threading.Tasks.Task<ManagedObjectReference> IdS_getSslCertificateManagerAsync(ManagedObjectReference _this)
    {
        return base.Channel.IdS_getSslCertificateManagerAsync(_this);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<IdS_probeConnectivityResponse> SsoPortType.IdS_probeConnectivityAsync(IdS_probeConnectivityRequest request)
    {
        return base.Channel.IdS_probeConnectivityAsync(request);
    }

    public System.Threading.Tasks.Task<IdS_probeConnectivityResponse> IdS_probeConnectivityAsync(ManagedObjectReference _this, string serviceUri, string authenticationType, SsoAdminIdentitySourceManagementServiceAuthenticationCredentials authnCredentials, string[] certificates)
    {
        IdS_probeConnectivityRequest inValue = new IdS_probeConnectivityRequest();
        inValue._this = _this;
        inValue.serviceUri = serviceUri;
        inValue.authenticationType = authenticationType;
        inValue.authnCredentials = authnCredentials;
        inValue.certificates = certificates;
        return ((SsoPortType)(this)).IdS_probeConnectivityAsync(inValue);
    }

    public System.Threading.Tasks.Task IdS_probeLdapConnectivityAsync(ManagedObjectReference _this, string domainName, SsoAdminIdentitySourceManagementServiceAuthenticationCredentials authnCredential, SsoAdminLdapIdentitySource identitySource)
    {
        return base.Channel.IdS_probeLdapConnectivityAsync(_this, domainName, authnCredential, identitySource);
    }

    public System.Threading.Tasks.Task<SsoAdminConfigurationManagementServiceCertificateChain> IdS_getSslIdentityAsync(ManagedObjectReference _this, string host, int ldapsPort)
    {
        return base.Channel.IdS_getSslIdentityAsync(_this, host, ldapsPort);
    }

    public System.Threading.Tasks.Task<SsoAdminLockoutPolicy> GetLockoutPolicyAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetLockoutPolicyAsync(_this);
    }

    public System.Threading.Tasks.Task UpdateLockoutPolicyAsync(ManagedObjectReference _this, SsoAdminLockoutPolicy policy)
    {
        return base.Channel.UpdateLockoutPolicyAsync(_this, policy);
    }

    public System.Threading.Tasks.Task UpdateLocalPasswordPolicyAsync(ManagedObjectReference _this, SsoAdminPasswordPolicy policy)
    {
        return base.Channel.UpdateLocalPasswordPolicyAsync(_this, policy);
    }

    public System.Threading.Tasks.Task<SsoAdminPasswordPolicy> GetLocalPasswordPolicyAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetLocalPasswordPolicyAsync(_this);
    }

    public System.Threading.Tasks.Task<SsoPrincipalId> LookupAsync(ManagedObjectReference _this, SsoPrincipalId id, bool isGroup)
    {
        return base.Channel.LookupAsync(_this, id, isGroup);
    }

    public System.Threading.Tasks.Task<SsoAdminPersonUser> FindPersonUserAsync(ManagedObjectReference _this, SsoPrincipalId userId)
    {
        return base.Channel.FindPersonUserAsync(_this, userId);
    }

    public System.Threading.Tasks.Task<SsoAdminPersonUser> FindSelfPersonUserAsync(ManagedObjectReference _this)
    {
        return base.Channel.FindSelfPersonUserAsync(_this);
    }

    public System.Threading.Tasks.Task<SsoAdminSolutionUser> FindSolutionUserAsync(ManagedObjectReference _this, string userName)
    {
        return base.Channel.FindSolutionUserAsync(_this, userName);
    }

    public System.Threading.Tasks.Task<SsoAdminSolutionUser> FindSolutionUserByCertDNAsync(ManagedObjectReference _this, string certDN)
    {
        return base.Channel.FindSolutionUserByCertDNAsync(_this, certDN);
    }

    public System.Threading.Tasks.Task<SsoAdminUser> FindUserAsync(ManagedObjectReference _this, SsoPrincipalId userId)
    {
        return base.Channel.FindUserAsync(_this, userId);
    }

    public System.Threading.Tasks.Task<SsoAdminGroup> FindGroupAsync(ManagedObjectReference _this, SsoPrincipalId groupId)
    {
        return base.Channel.FindGroupAsync(_this, groupId);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindPersonUsersResponse> SsoPortType.FindPersonUsersAsync(FindPersonUsersRequest request)
    {
        return base.Channel.FindPersonUsersAsync(request);
    }

    public System.Threading.Tasks.Task<FindPersonUsersResponse> FindPersonUsersAsync(ManagedObjectReference _this, SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria, int limit)
    {
        FindPersonUsersRequest inValue = new FindPersonUsersRequest();
        inValue._this = _this;
        inValue.criteria = criteria;
        inValue.limit = limit;
        return ((SsoPortType)(this)).FindPersonUsersAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindPersonUsersByNameResponse> SsoPortType.FindPersonUsersByNameAsync(FindPersonUsersByNameRequest request)
    {
        return base.Channel.FindPersonUsersByNameAsync(request);
    }

    public System.Threading.Tasks.Task<FindPersonUsersByNameResponse> FindPersonUsersByNameAsync(ManagedObjectReference _this, SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria, int limit)
    {
        FindPersonUsersByNameRequest inValue = new FindPersonUsersByNameRequest();
        inValue._this = _this;
        inValue.criteria = criteria;
        inValue.limit = limit;
        return ((SsoPortType)(this)).FindPersonUsersByNameAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindSolutionUsersResponse> SsoPortType.FindSolutionUsersAsync(FindSolutionUsersRequest request)
    {
        return base.Channel.FindSolutionUsersAsync(request);
    }

    public System.Threading.Tasks.Task<FindSolutionUsersResponse> FindSolutionUsersAsync(ManagedObjectReference _this, string searchString, int limit)
    {
        FindSolutionUsersRequest inValue = new FindSolutionUsersRequest();
        inValue._this = _this;
        inValue.searchString = searchString;
        inValue.limit = limit;
        return ((SsoPortType)(this)).FindSolutionUsersAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindUsersResponse> SsoPortType.FindUsersAsync(FindUsersRequest request)
    {
        return base.Channel.FindUsersAsync(request);
    }

    public System.Threading.Tasks.Task<FindUsersResponse> FindUsersAsync(ManagedObjectReference _this, SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria, int limit)
    {
        FindUsersRequest inValue = new FindUsersRequest();
        inValue._this = _this;
        inValue.criteria = criteria;
        inValue.limit = limit;
        return ((SsoPortType)(this)).FindUsersAsync(inValue);
    }

    public System.Threading.Tasks.Task<SsoAdminPrincipalDiscoveryServiceSearchResult> FindUserAccountAsync(ManagedObjectReference _this, string userName)
    {
        return base.Channel.FindUserAccountAsync(_this, userName);
    }

    public System.Threading.Tasks.Task<SsoAdminGroup> FindGroupAccountAsync(ManagedObjectReference _this, string groupName)
    {
        return base.Channel.FindGroupAccountAsync(_this, groupName);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindGroupsResponse> SsoPortType.FindGroupsAsync(FindGroupsRequest request)
    {
        return base.Channel.FindGroupsAsync(request);
    }

    public System.Threading.Tasks.Task<FindGroupsResponse> FindGroupsAsync(ManagedObjectReference _this, SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria, int limit)
    {
        FindGroupsRequest inValue = new FindGroupsRequest();
        inValue._this = _this;
        inValue.criteria = criteria;
        inValue.limit = limit;
        return ((SsoPortType)(this)).FindGroupsAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindGroupsByNameResponse> SsoPortType.FindGroupsByNameAsync(FindGroupsByNameRequest request)
    {
        return base.Channel.FindGroupsByNameAsync(request);
    }

    public System.Threading.Tasks.Task<FindGroupsByNameResponse> FindGroupsByNameAsync(ManagedObjectReference _this, SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria, int limit)
    {
        FindGroupsByNameRequest inValue = new FindGroupsByNameRequest();
        inValue._this = _this;
        inValue.criteria = criteria;
        inValue.limit = limit;
        return ((SsoPortType)(this)).FindGroupsByNameAsync(inValue);
    }

    public System.Threading.Tasks.Task<SsoAdminPrincipalDiscoveryServiceSearchResult> FindAsync(ManagedObjectReference _this, SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria, int limit)
    {
        return base.Channel.FindAsync(_this, criteria, limit);
    }

    public System.Threading.Tasks.Task<SsoAdminPrincipalDiscoveryServiceSearchResult> FindByNameAsync(ManagedObjectReference _this, SsoAdminPrincipalDiscoveryServiceSearchCriteria criteria, int limit)
    {
        return base.Channel.FindByNameAsync(_this, criteria, limit);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindUsersInGroupResponse> SsoPortType.FindUsersInGroupAsync(FindUsersInGroupRequest request)
    {
        return base.Channel.FindUsersInGroupAsync(request);
    }

    public System.Threading.Tasks.Task<FindUsersInGroupResponse> FindUsersInGroupAsync(ManagedObjectReference _this, SsoPrincipalId groupId, string searchString, int limit)
    {
        FindUsersInGroupRequest inValue = new FindUsersInGroupRequest();
        inValue._this = _this;
        inValue.groupId = groupId;
        inValue.searchString = searchString;
        inValue.limit = limit;
        return ((SsoPortType)(this)).FindUsersInGroupAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindPersonUsersByNameInGroupResponse> SsoPortType.FindPersonUsersByNameInGroupAsync(FindPersonUsersByNameInGroupRequest request)
    {
        return base.Channel.FindPersonUsersByNameInGroupAsync(request);
    }

    public System.Threading.Tasks.Task<FindPersonUsersByNameInGroupResponse> FindPersonUsersByNameInGroupAsync(ManagedObjectReference _this, SsoPrincipalId groupId, string searchString, int limit)
    {
        FindPersonUsersByNameInGroupRequest inValue = new FindPersonUsersByNameInGroupRequest();
        inValue._this = _this;
        inValue.groupId = groupId;
        inValue.searchString = searchString;
        inValue.limit = limit;
        return ((SsoPortType)(this)).FindPersonUsersByNameInGroupAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindPersonUsersInGroupResponse> SsoPortType.FindPersonUsersInGroupAsync(FindPersonUsersInGroupRequest request)
    {
        return base.Channel.FindPersonUsersInGroupAsync(request);
    }

    public System.Threading.Tasks.Task<FindPersonUsersInGroupResponse> FindPersonUsersInGroupAsync(ManagedObjectReference _this, SsoPrincipalId groupId, string searchString, int limit)
    {
        FindPersonUsersInGroupRequest inValue = new FindPersonUsersInGroupRequest();
        inValue._this = _this;
        inValue.groupId = groupId;
        inValue.searchString = searchString;
        inValue.limit = limit;
        return ((SsoPortType)(this)).FindPersonUsersInGroupAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindSolutionUsersInGroupResponse> SsoPortType.FindSolutionUsersInGroupAsync(FindSolutionUsersInGroupRequest request)
    {
        return base.Channel.FindSolutionUsersInGroupAsync(request);
    }

    public System.Threading.Tasks.Task<FindSolutionUsersInGroupResponse> FindSolutionUsersInGroupAsync(ManagedObjectReference _this, string groupName, string searchString, int limit)
    {
        FindSolutionUsersInGroupRequest inValue = new FindSolutionUsersInGroupRequest();
        inValue._this = _this;
        inValue.groupName = groupName;
        inValue.searchString = searchString;
        inValue.limit = limit;
        return ((SsoPortType)(this)).FindSolutionUsersInGroupAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindGroupsInGroupResponse> SsoPortType.FindGroupsInGroupAsync(FindGroupsInGroupRequest request)
    {
        return base.Channel.FindGroupsInGroupAsync(request);
    }

    public System.Threading.Tasks.Task<FindGroupsInGroupResponse> FindGroupsInGroupAsync(ManagedObjectReference _this, SsoPrincipalId groupId, string searchString, int limit)
    {
        FindGroupsInGroupRequest inValue = new FindGroupsInGroupRequest();
        inValue._this = _this;
        inValue.groupId = groupId;
        inValue.searchString = searchString;
        inValue.limit = limit;
        return ((SsoPortType)(this)).FindGroupsInGroupAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindGroupsByNameInGroupResponse> SsoPortType.FindGroupsByNameInGroupAsync(FindGroupsByNameInGroupRequest request)
    {
        return base.Channel.FindGroupsByNameInGroupAsync(request);
    }

    public System.Threading.Tasks.Task<FindGroupsByNameInGroupResponse> FindGroupsByNameInGroupAsync(ManagedObjectReference _this, SsoPrincipalId groupId, string searchString, int limit)
    {
        FindGroupsByNameInGroupRequest inValue = new FindGroupsByNameInGroupRequest();
        inValue._this = _this;
        inValue.groupId = groupId;
        inValue.searchString = searchString;
        inValue.limit = limit;
        return ((SsoPortType)(this)).FindGroupsByNameInGroupAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindDirectParentGroupsResponse> SsoPortType.FindDirectParentGroupsAsync(FindDirectParentGroupsRequest request)
    {
        return base.Channel.FindDirectParentGroupsAsync(request);
    }

    public System.Threading.Tasks.Task<FindDirectParentGroupsResponse> FindDirectParentGroupsAsync(ManagedObjectReference _this, SsoPrincipalId principalId)
    {
        FindDirectParentGroupsRequest inValue = new FindDirectParentGroupsRequest();
        inValue._this = _this;
        inValue.principalId = principalId;
        return ((SsoPortType)(this)).FindDirectParentGroupsAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindNestedParentGroupsResponse> SsoPortType.FindNestedParentGroupsAsync(FindNestedParentGroupsRequest request)
    {
        return base.Channel.FindNestedParentGroupsAsync(request);
    }

    public System.Threading.Tasks.Task<FindNestedParentGroupsResponse> FindNestedParentGroupsAsync(ManagedObjectReference _this, SsoPrincipalId userId)
    {
        FindNestedParentGroupsRequest inValue = new FindNestedParentGroupsRequest();
        inValue._this = _this;
        inValue.userId = userId;
        return ((SsoPortType)(this)).FindNestedParentGroupsAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindLockedUsersResponse> SsoPortType.FindLockedUsersAsync(FindLockedUsersRequest request)
    {
        return base.Channel.FindLockedUsersAsync(request);
    }

    public System.Threading.Tasks.Task<FindLockedUsersResponse> FindLockedUsersAsync(ManagedObjectReference _this, string searchString, int limit)
    {
        FindLockedUsersRequest inValue = new FindLockedUsersRequest();
        inValue._this = _this;
        inValue.searchString = searchString;
        inValue.limit = limit;
        return ((SsoPortType)(this)).FindLockedUsersAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindDisabledPersonUsersResponse> SsoPortType.FindDisabledPersonUsersAsync(FindDisabledPersonUsersRequest request)
    {
        return base.Channel.FindDisabledPersonUsersAsync(request);
    }

    public System.Threading.Tasks.Task<FindDisabledPersonUsersResponse> FindDisabledPersonUsersAsync(ManagedObjectReference _this, string searchString, int limit)
    {
        FindDisabledPersonUsersRequest inValue = new FindDisabledPersonUsersRequest();
        inValue._this = _this;
        inValue.searchString = searchString;
        inValue.limit = limit;
        return ((SsoPortType)(this)).FindDisabledPersonUsersAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindDisabledSolutionUsersResponse> SsoPortType.FindDisabledSolutionUsersAsync(FindDisabledSolutionUsersRequest request)
    {
        return base.Channel.FindDisabledSolutionUsersAsync(request);
    }

    public System.Threading.Tasks.Task<FindDisabledSolutionUsersResponse> FindDisabledSolutionUsersAsync(ManagedObjectReference _this, string searchString)
    {
        FindDisabledSolutionUsersRequest inValue = new FindDisabledSolutionUsersRequest();
        inValue._this = _this;
        inValue.searchString = searchString;
        return ((SsoPortType)(this)).FindDisabledSolutionUsersAsync(inValue);
    }

    public System.Threading.Tasks.Task<SsoAdminPersonUser> FindRegisteredExternalIDPUserAsync(ManagedObjectReference _this, SsoPrincipalId userId)
    {
        return base.Channel.FindRegisteredExternalIDPUserAsync(_this, userId);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<GetImplicitGroupNamesResponse> SsoPortType.GetImplicitGroupNamesAsync(GetImplicitGroupNamesRequest request)
    {
        return base.Channel.GetImplicitGroupNamesAsync(request);
    }

    public System.Threading.Tasks.Task<GetImplicitGroupNamesResponse> GetImplicitGroupNamesAsync(ManagedObjectReference _this)
    {
        GetImplicitGroupNamesRequest inValue = new GetImplicitGroupNamesRequest();
        inValue._this = _this;
        return ((SsoPortType)(this)).GetImplicitGroupNamesAsync(inValue);
    }

    public System.Threading.Tasks.Task<SsoPrincipalId> CreateLocalPersonUserAsync(ManagedObjectReference _this, string userName, SsoAdminPersonDetails userDetails, string password)
    {
        return base.Channel.CreateLocalPersonUserAsync(_this, userName, userDetails, password);
    }

    public System.Threading.Tasks.Task<SsoPrincipalId> CreateLocalSolutionUserAsync(ManagedObjectReference _this, string userName, SsoAdminSolutionDetails userDetails, bool external)
    {
        return base.Channel.CreateLocalSolutionUserAsync(_this, userName, userDetails, external);
    }

    public System.Threading.Tasks.Task<SsoPrincipalId> CreateLocalGroupAsync(ManagedObjectReference _this, string groupName, SsoAdminGroupDetails groupDetails)
    {
        return base.Channel.CreateLocalGroupAsync(_this, groupName, groupDetails);
    }

    public System.Threading.Tasks.Task DeleteLocalPrincipalAsync(ManagedObjectReference _this, string principalName)
    {
        return base.Channel.DeleteLocalPrincipalAsync(_this, principalName);
    }

    public System.Threading.Tasks.Task<bool> RemoveFromLocalGroupAsync(ManagedObjectReference _this, SsoPrincipalId principalId, string groupName)
    {
        return base.Channel.RemoveFromLocalGroupAsync(_this, principalId, groupName);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<RemovePrincipalsFromLocalGroupResponse> SsoPortType.RemovePrincipalsFromLocalGroupAsync(RemovePrincipalsFromLocalGroupRequest request)
    {
        return base.Channel.RemovePrincipalsFromLocalGroupAsync(request);
    }

    public System.Threading.Tasks.Task<RemovePrincipalsFromLocalGroupResponse> RemovePrincipalsFromLocalGroupAsync(ManagedObjectReference _this, SsoPrincipalId[] principalsIds, string groupName)
    {
        RemovePrincipalsFromLocalGroupRequest inValue = new RemovePrincipalsFromLocalGroupRequest();
        inValue._this = _this;
        inValue.principalsIds = principalsIds;
        inValue.groupName = groupName;
        return ((SsoPortType)(this)).RemovePrincipalsFromLocalGroupAsync(inValue);
    }

    public System.Threading.Tasks.Task<bool> AddUserToLocalGroupAsync(ManagedObjectReference _this, SsoPrincipalId userId, string groupName)
    {
        return base.Channel.AddUserToLocalGroupAsync(_this, userId, groupName);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<AddUsersToLocalGroupResponse> SsoPortType.AddUsersToLocalGroupAsync(AddUsersToLocalGroupRequest request)
    {
        return base.Channel.AddUsersToLocalGroupAsync(request);
    }

    public System.Threading.Tasks.Task<AddUsersToLocalGroupResponse> AddUsersToLocalGroupAsync(ManagedObjectReference _this, SsoPrincipalId[] userIds, string groupName)
    {
        AddUsersToLocalGroupRequest inValue = new AddUsersToLocalGroupRequest();
        inValue._this = _this;
        inValue.userIds = userIds;
        inValue.groupName = groupName;
        return ((SsoPortType)(this)).AddUsersToLocalGroupAsync(inValue);
    }

    public System.Threading.Tasks.Task<bool> AddGroupToLocalGroupAsync(ManagedObjectReference _this, SsoPrincipalId groupId, string groupName)
    {
        return base.Channel.AddGroupToLocalGroupAsync(_this, groupId, groupName);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<AddGroupsToLocalGroupResponse> SsoPortType.AddGroupsToLocalGroupAsync(AddGroupsToLocalGroupRequest request)
    {
        return base.Channel.AddGroupsToLocalGroupAsync(request);
    }

    public System.Threading.Tasks.Task<AddGroupsToLocalGroupResponse> AddGroupsToLocalGroupAsync(ManagedObjectReference _this, SsoPrincipalId[] groupIds, string groupName)
    {
        AddGroupsToLocalGroupRequest inValue = new AddGroupsToLocalGroupRequest();
        inValue._this = _this;
        inValue.groupIds = groupIds;
        inValue.groupName = groupName;
        return ((SsoPortType)(this)).AddGroupsToLocalGroupAsync(inValue);
    }

    public System.Threading.Tasks.Task<SsoPrincipalId> UpdateLocalPersonUserDetailsAsync(ManagedObjectReference _this, string userName, SsoAdminPersonDetails userDetails)
    {
        return base.Channel.UpdateLocalPersonUserDetailsAsync(_this, userName, userDetails);
    }

    public System.Threading.Tasks.Task ResetLocalPersonUserPasswordAsync(ManagedObjectReference _this, string userName, string newPassword)
    {
        return base.Channel.ResetLocalPersonUserPasswordAsync(_this, userName, newPassword);
    }

    public System.Threading.Tasks.Task<SsoPrincipalId> UpdateLocalSolutionUserDetailsAsync(ManagedObjectReference _this, string userName, SsoAdminSolutionDetails userDetails)
    {
        return base.Channel.UpdateLocalSolutionUserDetailsAsync(_this, userName, userDetails);
    }

    public System.Threading.Tasks.Task<SsoPrincipalId> UpdateLocalGroupDetailsAsync(ManagedObjectReference _this, string groupName, SsoAdminGroupDetails groupDetails)
    {
        return base.Channel.UpdateLocalGroupDetailsAsync(_this, groupName, groupDetails);
    }

    public System.Threading.Tasks.Task<SsoPrincipalId> UpdateSelfLocalPersonUserDetailsAsync(ManagedObjectReference _this, SsoAdminPersonDetails userDetails)
    {
        return base.Channel.UpdateSelfLocalPersonUserDetailsAsync(_this, userDetails);
    }

    public System.Threading.Tasks.Task DeleteSelfSolutionUserAsync(ManagedObjectReference _this)
    {
        return base.Channel.DeleteSelfSolutionUserAsync(_this);
    }

    public System.Threading.Tasks.Task ResetSelfLocalPersonUserPasswordAsync(ManagedObjectReference _this, string newPassword)
    {
        return base.Channel.ResetSelfLocalPersonUserPasswordAsync(_this, newPassword);
    }

    public System.Threading.Tasks.Task ResetLocalUserPasswordAsync(ManagedObjectReference _this, string username, string currentPassword, string newPassword)
    {
        return base.Channel.ResetLocalUserPasswordAsync(_this, username, currentPassword, newPassword);
    }

    public System.Threading.Tasks.Task<SsoPrincipalId> UpdateSelfLocalSolutionUserDetailsAsync(ManagedObjectReference _this, SsoAdminSolutionDetails userDetails)
    {
        return base.Channel.UpdateSelfLocalSolutionUserDetailsAsync(_this, userDetails);
    }

    public System.Threading.Tasks.Task<bool> UnlockUserAccountAsync(ManagedObjectReference _this, SsoPrincipalId userId)
    {
        return base.Channel.UnlockUserAccountAsync(_this, userId);
    }

    public System.Threading.Tasks.Task<bool> EnableUserAccountAsync(ManagedObjectReference _this, SsoPrincipalId userId)
    {
        return base.Channel.EnableUserAccountAsync(_this, userId);
    }

    public System.Threading.Tasks.Task<bool> DisableUserAccountAsync(ManagedObjectReference _this, SsoPrincipalId userId)
    {
        return base.Channel.DisableUserAccountAsync(_this, userId);
    }

    public System.Threading.Tasks.Task<int> GetDaysRemainingUntilPasswordExpirationAsync(ManagedObjectReference _this, SsoPrincipalId userId)
    {
        return base.Channel.GetDaysRemainingUntilPasswordExpirationAsync(_this, userId);
    }

    public System.Threading.Tasks.Task<int> GetDaysRemainingUntilSelfPasswordExpirationAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetDaysRemainingUntilSelfPasswordExpirationAsync(_this);
    }

    public System.Threading.Tasks.Task<bool> RegisterExternalUserAsync(ManagedObjectReference _this, SsoPrincipalId externalUserId)
    {
        return base.Channel.RegisterExternalUserAsync(_this, externalUserId);
    }

    public System.Threading.Tasks.Task<bool> RemoveExternalUserAsync(ManagedObjectReference _this, SsoPrincipalId externalUserId)
    {
        return base.Channel.RemoveExternalUserAsync(_this, externalUserId);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<ExportFullStateResponse> SsoPortType.ExportFullStateAsync(ExportFullStateRequest request)
    {
        return base.Channel.ExportFullStateAsync(request);
    }

    public System.Threading.Tasks.Task<ExportFullStateResponse> ExportFullStateAsync(ManagedObjectReference _this)
    {
        ExportFullStateRequest inValue = new ExportFullStateRequest();
        inValue._this = _this;
        return ((SsoPortType)(this)).ExportFullStateAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<ImportFullStateResponse> SsoPortType.ImportFullStateAsync(ImportFullStateRequest request)
    {
        return base.Channel.ImportFullStateAsync(request);
    }

    public System.Threading.Tasks.Task<ImportFullStateResponse> ImportFullStateAsync(ManagedObjectReference _this, byte[] fullState)
    {
        ImportFullStateRequest inValue = new ImportFullStateRequest();
        inValue._this = _this;
        inValue.fullState = fullState;
        return ((SsoPortType)(this)).ImportFullStateAsync(inValue);
    }

    public System.Threading.Tasks.Task<bool> SetRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId, string role)
    {
        return base.Channel.SetRoleAsync(_this, userId, role);
    }

    public System.Threading.Tasks.Task<bool> HasAdministratorRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId)
    {
        return base.Channel.HasAdministratorRoleAsync(_this, userId);
    }

    public System.Threading.Tasks.Task<bool> HasConfigurationUserRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId)
    {
        return base.Channel.HasConfigurationUserRoleAsync(_this, userId);
    }

    public System.Threading.Tasks.Task<bool> HasRegularUserRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId)
    {
        return base.Channel.HasRegularUserRoleAsync(_this, userId);
    }

    public System.Threading.Tasks.Task<bool> GrantWSTrustRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId, string role)
    {
        return base.Channel.GrantWSTrustRoleAsync(_this, userId, role);
    }

    public System.Threading.Tasks.Task<bool> RevokeWSTrustRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId, string role)
    {
        return base.Channel.RevokeWSTrustRoleAsync(_this, userId, role);
    }

    public System.Threading.Tasks.Task<bool> GrantIDPProvisioningRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId, string role)
    {
        return base.Channel.GrantIDPProvisioningRoleAsync(_this, userId, role);
    }

    public System.Threading.Tasks.Task<bool> RevokeIDPProvisioningRoleAsync(ManagedObjectReference _this, SsoPrincipalId userId, string role)
    {
        return base.Channel.RevokeIDPProvisioningRoleAsync(_this, userId, role);
    }

    public System.Threading.Tasks.Task<SsoAdminServiceContent> SsoAdminServiceInstanceAsync(ManagedObjectReference _this)
    {
        return base.Channel.SsoAdminServiceInstanceAsync(_this);
    }

    public System.Threading.Tasks.Task<SsoAdminSmtpConfig> GetSmtpConfigurationAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetSmtpConfigurationAsync(_this);
    }

    public System.Threading.Tasks.Task UpdateSmtpConfigurationAsync(ManagedObjectReference _this, SsoAdminSmtpConfig config)
    {
        return base.Channel.UpdateSmtpConfigurationAsync(_this, config);
    }

    public System.Threading.Tasks.Task SendMailAsync(ManagedObjectReference _this, SsoAdminMailContent content)
    {
        return base.Channel.SendMailAsync(_this, content);
    }

    public System.Threading.Tasks.Task<SsoAdminSsoHealthStats> GetSsoStatisticsAsync(ManagedObjectReference _this)
    {
        return base.Channel.GetSsoStatisticsAsync(_this);
    }

    public System.Threading.Tasks.Task<SsoAdminActiveDirectoryJoinInfo> IdS_getActiveDirectoryJoinStatusAsync(ManagedObjectReference _this)
    {
        return base.Channel.IdS_getActiveDirectoryJoinStatusAsync(_this);
    }

    public System.Threading.Tasks.Task JoinActiveDirectoryAsync(ManagedObjectReference _this, string username, string password, string domain, string orgUnit)
    {
        return base.Channel.JoinActiveDirectoryAsync(_this, username, password, domain, orgUnit);
    }

    public System.Threading.Tasks.Task LeaveActiveDirectoryAsync(ManagedObjectReference _this, string username, string password)
    {
        return base.Channel.LeaveActiveDirectoryAsync(_this, username, password);
    }

    public System.Threading.Tasks.Task<bool> IsMemberOfGroupAsync(ManagedObjectReference _this, SsoPrincipalId userId, SsoPrincipalId groupId)
    {
        return base.Channel.IsMemberOfGroupAsync(_this, userId, groupId);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindParentGroupsResponse> SsoPortType.FindParentGroupsAsync(FindParentGroupsRequest request)
    {
        return base.Channel.FindParentGroupsAsync(request);
    }

    public System.Threading.Tasks.Task<FindParentGroupsResponse> FindParentGroupsAsync(ManagedObjectReference _this, SsoPrincipalId userId, SsoPrincipalId[] groupList)
    {
        FindParentGroupsRequest inValue = new FindParentGroupsRequest();
        inValue._this = _this;
        inValue.userId = userId;
        inValue.groupList = groupList;
        return ((SsoPortType)(this)).FindParentGroupsAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<FindAllParentGroupsResponse> SsoPortType.FindAllParentGroupsAsync(FindAllParentGroupsRequest request)
    {
        return base.Channel.FindAllParentGroupsAsync(request);
    }

    public System.Threading.Tasks.Task<FindAllParentGroupsResponse> FindAllParentGroupsAsync(ManagedObjectReference _this, SsoPrincipalId userId)
    {
        FindAllParentGroupsRequest inValue = new FindAllParentGroupsRequest();
        inValue._this = _this;
        inValue.userId = userId;
        return ((SsoPortType)(this)).FindAllParentGroupsAsync(inValue);
    }

    public System.Threading.Tasks.Task<SsoGroupcheckServiceContent> SsoGroupcheckServiceInstanceAsync(ManagedObjectReference _this)
    {
        return base.Channel.SsoGroupcheckServiceInstanceAsync(_this);
    }

    public virtual System.Threading.Tasks.Task OpenAsync()
    {
        return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
    }

    public virtual System.Threading.Tasks.Task CloseAsync()
    {
        return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
    }

    private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
    {
        if ((endpointConfiguration == EndpointConfiguration.SsoPort))
        {
            System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
            result.MaxBufferSize = int.MaxValue;
            result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
            result.MaxReceivedMessageSize = int.MaxValue;
            result.AllowCookies = true;
            result.Security.Mode = System.ServiceModel.BasicHttpSecurityMode.Transport;
            return result;
        }
        throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
    }

    private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
    {
        if ((endpointConfiguration == EndpointConfiguration.SsoPort))
        {
            return new System.ServiceModel.EndpointAddress("https://localhost/sdk/ssoService");
        }
        throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
    }

    private static System.ServiceModel.Channels.Binding GetDefaultBinding()
    {
        return SsoPortTypeClient.GetBindingForEndpoint(EndpointConfiguration.SsoPort);
    }

    private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
    {
        return SsoPortTypeClient.GetEndpointAddress(EndpointConfiguration.SsoPort);
    }

    public enum EndpointConfiguration
    {

        SsoPort,
    }
}
}