{"Base": {"Name": "FulClnJSON", "DisplayName": "FullClonePraJSON", "AccessGroup": "Root", "Description": "create full clone via JSON"}, "DesktopSettings": {"enabled": true, "deleting": false, "connectionServerRestrictions": null, "logoffSettings": {"powerPolicy": "TAKE_NO_POWER_ACTION", "automaticLogoffPolicy": "NEVER", "automaticLogoffMinutes": 120, "allowUsersToResetMachines": false, "allowMultipleSessionsPerUser": false, "deleteOrRefreshMachineAfterLogoff": "NEVER", "refreshOsDiskAfterLogoff": "NEVER", "refreshPeriodDaysForReplicaOsDisk": 5, "refreshThresholdPercentageForReplicaOsDisk": 10}, "displayProtocolSettings": {"supportedDisplayProtocols": ["PCOIP", "BLAST"], "defaultDisplayProtocol": "BLAST", "allowUsersToChooseProtocol": true, "pcoipDisplaySettings": {"renderer3D": "DISABLED", "enableGRIDvGPUs": false, "vRamSizeMB": 96, "maxNumberOfMonitors": 3, "maxResolutionOfAnyOneMonitor": "WSXGA_PLUS"}, "enableHTMLAccess": true}, "flashSettings": {"quality": "NO_CONTROL", "throttling": "DISABLED"}, "mirageConfigurationOverrides": {"overrideGlobalSetting": false, "enabled": false, "url": false}}, "Type": "AUTOMATED", "AutomatedDesktopSpec": {"ProvisioningType": "VIRTUAL_CENTER", "VirtualCenter": null, "UserAssignment": {"UserAssignment": "DEDICATED", "AutomaticAssignment": true}, "VmNamingSpec": {"NamingMethod": "PATTERN", "PatternNamingSettings": {"NamingPattern": "FullClnJson1", "MaxNumberOfMachines": 1, "NumberOfSpareMachines": 1, "ProvisioningTime": "UP_FRONT", "MinNumberOfMachines": null}, "SpecificNamingSpec": null}, "VirtualCenterProvisioningSettings": {"EnableProvisioning": true, "StopProvisioningOnError": true, "MinReadyVMsOnVComposerMaintenance": 0, "VirtualCenterProvisioningData": {"Template": "powerCLI-VM-TEMPLATE", "ParentVm": null, "Snapshot": null, "Datacenter": null, "VmFolder": "<PERSON><PERSON><PERSON>", "HostOrCluster": "CS-1", "ResourcePool": "CS-1"}, "VirtualCenterStorageSettings": {"Datastores": [{"Datastore": "datastore1", "StorageOvercommit": "UNBOUNDED"}], "UseVSan": false, "ViewComposerStorageSettings": null, "ViewStorageAcceleratorSettings": {"UseViewStorageAccelerator": false, "ViewComposerDiskTypes": null, "RegenerateViewStorageAcceleratorDays": null, "BlackoutTimes": null}}, "VirtualCenterNetworkingSettings": {"Nics": null}}, "VirtualCenterManagedCommonSettings": {"TransparentPageSharingScope": "VM"}, "CustomizationSettings": {"CustomizationType": "SYS_PREP", "DomainAdministrator": null, "AdContainer": "CN=Computers", "ReusePreExistingAccounts": false, "NoCustomizationSettings": {"DoNotPowerOnVMsAfterCreation": false}, "SysprepCustomizationSettings": {"customizationSpec": "praveencust"}, "QuickprepCustomizationSettings": null, "CloneprepCustomizationSettings": null}}, "ManualDesktopSpec": null, "RdsDesktopSpec": null, "GlobalEntitlementData": null, "NetBiosName": "adviewdev"}