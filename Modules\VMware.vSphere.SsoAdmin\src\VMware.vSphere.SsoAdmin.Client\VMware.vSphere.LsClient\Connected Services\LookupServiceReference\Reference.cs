﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     //
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace LookupServiceReference
{
    
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(UnexpectedFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SystemError))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SecurityError))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(RequestCanceled))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(NotSupported))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(NotImplemented))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(NotEnoughLicenses))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ManagedObjectNotFound))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MethodNotFound))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidArgument))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(HostCommunication))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(HostNotReachable))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(HostNotConnected))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class RuntimeFault : MethodFault
    {
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupFaultServiceFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupFaultUnsupportedSiteFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupFaultEntryNotFoundFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupFaultEntryExistsFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(RuntimeFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(UnexpectedFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SystemError))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SecurityError))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(RequestCanceled))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(NotSupported))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(NotImplemented))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(NotEnoughLicenses))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ManagedObjectNotFound))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MethodNotFound))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidArgument))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(HostCommunication))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(HostNotReachable))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(HostNotConnected))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class MethodFault
    {
        
        private LocalizedMethodFault faultCauseField;
        
        private LocalizableMessage[] faultMessageField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public LocalizedMethodFault faultCause
        {
            get
            {
                return this.faultCauseField;
            }
            set
            {
                this.faultCauseField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("faultMessage", Order=1)]
        public LocalizableMessage[] faultMessage
        {
            get
            {
                return this.faultMessageField;
            }
            set
            {
                this.faultMessageField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LocalizedMethodFault : DynamicData
    {
        
        private MethodFault faultField;
        
        private string localizedMessageField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public MethodFault fault
        {
            get
            {
                return this.faultField;
            }
            set
            {
                this.faultField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string localizedMessage
        {
            get
            {
                return this.localizedMessageField;
            }
            set
            {
                this.localizedMessageField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceContent))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupHaBackupNodeConfiguration))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationFilter))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationServiceType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationAttribute))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationEndpointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationEndpoint))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationMutableServiceInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationSetSpec))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationCommonServiceInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationCreateSpec))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LocalizedMethodFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LocalizableMessage))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(KeyAnyValue))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class DynamicData
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class GetSiteIdRequestType
    {
        
        private ManagedObjectReference _thisField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ManagedObjectReference _this
        {
            get
            {
                return this._thisField;
            }
            set
            {
                this._thisField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class ManagedObjectReference
    {
        
        private string typeField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string type
        {
            get
            {
                return this.typeField;
            }
            set
            {
                this.typeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class ListRequestType
    {
        
        private ManagedObjectReference _thisField;
        
        private LookupServiceRegistrationFilter filterCriteriaField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ManagedObjectReference _this
        {
            get
            {
                return this._thisField;
            }
            set
            {
                this._thisField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public LookupServiceRegistrationFilter filterCriteria
        {
            get
            {
                return this.filterCriteriaField;
            }
            set
            {
                this.filterCriteriaField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupServiceRegistrationFilter : DynamicData
    {
        
        private string siteIdField;
        
        private string nodeIdField;
        
        private LookupServiceRegistrationServiceType serviceTypeField;
        
        private LookupServiceRegistrationEndpointType endpointTypeField;
        
        private string endpointTrustAnchorField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string siteId
        {
            get
            {
                return this.siteIdField;
            }
            set
            {
                this.siteIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string nodeId
        {
            get
            {
                return this.nodeIdField;
            }
            set
            {
                this.nodeIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public LookupServiceRegistrationServiceType serviceType
        {
            get
            {
                return this.serviceTypeField;
            }
            set
            {
                this.serviceTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public LookupServiceRegistrationEndpointType endpointType
        {
            get
            {
                return this.endpointTypeField;
            }
            set
            {
                this.endpointTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string endpointTrustAnchor
        {
            get
            {
                return this.endpointTrustAnchorField;
            }
            set
            {
                this.endpointTrustAnchorField = value;
            }
        }

   }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupServiceRegistrationServiceType : DynamicData
    {
        
        private string productField;
        
        private string typeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string product
        {
            get
            {
                return this.productField;
            }
            set
            {
                this.productField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string type
        {
            get
            {
                return this.typeField;
            }
            set
            {
                this.typeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupServiceRegistrationEndpointType : DynamicData
    {
        
        private string protocolField;
        
        private string typeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string protocol
        {
            get
            {
                return this.protocolField;
            }
            set
            {
                this.protocolField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string type
        {
            get
            {
                return this.typeField;
            }
            set
            {
                this.typeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class GetRequestType
    {
        
        private ManagedObjectReference _thisField;
        
        private string serviceIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ManagedObjectReference _this
        {
            get
            {
                return this._thisField;
            }
            set
            {
                this._thisField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string serviceId
        {
            get
            {
                return this.serviceIdField;
            }
            set
            {
                this.serviceIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class SetTrustAnchorRequestType
    {
        
        private ManagedObjectReference _thisField;
        
        private LookupServiceRegistrationFilter filterField;
        
        private string[] trustAnchorsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ManagedObjectReference _this
        {
            get
            {
                return this._thisField;
            }
            set
            {
                this._thisField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public LookupServiceRegistrationFilter filter
        {
            get
            {
                return this.filterField;
            }
            set
            {
                this.filterField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("trustAnchors", Order=2)]
        public string[] trustAnchors
        {
            get
            {
                return this.trustAnchorsField;
            }
            set
            {
                this.trustAnchorsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class SetRequestType
    {
        
        private ManagedObjectReference _thisField;
        
        private string serviceIdField;
        
        private LookupServiceRegistrationSetSpec serviceSpecField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ManagedObjectReference _this
        {
            get
            {
                return this._thisField;
            }
            set
            {
                this._thisField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string serviceId
        {
            get
            {
                return this.serviceIdField;
            }
            set
            {
                this.serviceIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public LookupServiceRegistrationSetSpec serviceSpec
        {
            get
            {
                return this.serviceSpecField;
            }
            set
            {
                this.serviceSpecField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupServiceRegistrationSetSpec : LookupServiceRegistrationMutableServiceInfo
    {
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationSetSpec))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationCommonServiceInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationCreateSpec))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupServiceRegistrationMutableServiceInfo : DynamicData
    {
        
        private string serviceVersionField;
        
        private string vendorNameResourceKeyField;
        
        private string vendorNameDefaultField;
        
        private string vendorProductInfoResourceKeyField;
        
        private string vendorProductInfoDefaultField;
        
        private LookupServiceRegistrationEndpoint[] serviceEndpointsField;
        
        private LookupServiceRegistrationAttribute[] serviceAttributesField;
        
        private string serviceNameResourceKeyField;
        
        private string serviceNameDefaultField;
        
        private string serviceDescriptionResourceKeyField;
        
        private string serviceDescriptionDefaultField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string serviceVersion
        {
            get
            {
                return this.serviceVersionField;
            }
            set
            {
                this.serviceVersionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string vendorNameResourceKey
        {
            get
            {
                return this.vendorNameResourceKeyField;
            }
            set
            {
                this.vendorNameResourceKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string vendorNameDefault
        {
            get
            {
                return this.vendorNameDefaultField;
            }
            set
            {
                this.vendorNameDefaultField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string vendorProductInfoResourceKey
        {
            get
            {
                return this.vendorProductInfoResourceKeyField;
            }
            set
            {
                this.vendorProductInfoResourceKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string vendorProductInfoDefault
        {
            get
            {
                return this.vendorProductInfoDefaultField;
            }
            set
            {
                this.vendorProductInfoDefaultField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("serviceEndpoints", Order=5)]
        public LookupServiceRegistrationEndpoint[] serviceEndpoints
        {
            get
            {
                return this.serviceEndpointsField;
            }
            set
            {
                this.serviceEndpointsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("serviceAttributes", Order=6)]
        public LookupServiceRegistrationAttribute[] serviceAttributes
        {
            get
            {
                return this.serviceAttributesField;
            }
            set
            {
                this.serviceAttributesField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string serviceNameResourceKey
        {
            get
            {
                return this.serviceNameResourceKeyField;
            }
            set
            {
                this.serviceNameResourceKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string serviceNameDefault
        {
            get
            {
                return this.serviceNameDefaultField;
            }
            set
            {
                this.serviceNameDefaultField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string serviceDescriptionResourceKey
        {
            get
            {
                return this.serviceDescriptionResourceKeyField;
            }
            set
            {
                this.serviceDescriptionResourceKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public string serviceDescriptionDefault
        {
            get
            {
                return this.serviceDescriptionDefaultField;
            }
            set
            {
                this.serviceDescriptionDefaultField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupServiceRegistrationEndpoint : DynamicData
    {
        
        private string urlField;
        
        private LookupServiceRegistrationEndpointType endpointTypeField;
        
        private string[] sslTrustField;
        
        private LookupServiceRegistrationAttribute[] endpointAttributesField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=0)]
        public string url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public LookupServiceRegistrationEndpointType endpointType
        {
            get
            {
                return this.endpointTypeField;
            }
            set
            {
                this.endpointTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("sslTrust", Order=2)]
        public string[] sslTrust
        {
            get
            {
                return this.sslTrustField;
            }
            set
            {
                this.sslTrustField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("endpointAttributes", Order=3)]
        public LookupServiceRegistrationAttribute[] endpointAttributes
        {
            get
            {
                return this.endpointAttributesField;
            }
            set
            {
                this.endpointAttributesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupServiceRegistrationAttribute : DynamicData
    {
        
        private string keyField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string key
        {
            get
            {
                return this.keyField;
            }
            set
            {
                this.keyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupServiceRegistrationCreateSpec))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupServiceRegistrationCommonServiceInfo : LookupServiceRegistrationMutableServiceInfo
    {
        
        private string ownerIdField;
        
        private LookupServiceRegistrationServiceType serviceTypeField;
        
        private string nodeIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string ownerId
        {
            get
            {
                return this.ownerIdField;
            }
            set
            {
                this.ownerIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public LookupServiceRegistrationServiceType serviceType
        {
            get
            {
                return this.serviceTypeField;
            }
            set
            {
                this.serviceTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string nodeId
        {
            get
            {
                return this.nodeIdField;
            }
            set
            {
                this.nodeIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupServiceRegistrationInfo : LookupServiceRegistrationCommonServiceInfo
    {
        
        private string serviceIdField;
        
        private string siteIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string serviceId
        {
            get
            {
                return this.serviceIdField;
            }
            set
            {
                this.serviceIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string siteId
        {
            get
            {
                return this.siteIdField;
            }
            set
            {
                this.siteIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupServiceRegistrationCreateSpec : LookupServiceRegistrationCommonServiceInfo
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class DeleteRequestType
    {
        
        private ManagedObjectReference _thisField;
        
        private string serviceIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ManagedObjectReference _this
        {
            get
            {
                return this._thisField;
            }
            set
            {
                this._thisField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string serviceId
        {
            get
            {
                return this.serviceIdField;
            }
            set
            {
                this.serviceIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class CreateRequestType
    {
        
        private ManagedObjectReference _thisField;
        
        private string serviceIdField;
        
        private LookupServiceRegistrationCreateSpec createSpecField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ManagedObjectReference _this
        {
            get
            {
                return this._thisField;
            }
            set
            {
                this._thisField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string serviceId
        {
            get
            {
                return this.serviceIdField;
            }
            set
            {
                this.serviceIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public LookupServiceRegistrationCreateSpec createSpec
        {
            get
            {
                return this.createSpecField;
            }
            set
            {
                this.createSpecField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class RetrieveServiceContentRequestType
    {
        
        private ManagedObjectReference _thisField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ManagedObjectReference _this
        {
            get
            {
                return this._thisField;
            }
            set
            {
                this._thisField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class GetLocaleRequestType
    {
        
        private ManagedObjectReference _thisField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ManagedObjectReference _this
        {
            get
            {
                return this._thisField;
            }
            set
            {
                this._thisField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class SetLocaleRequestType
    {
        
        private ManagedObjectReference _thisField;
        
        private string localeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ManagedObjectReference _this
        {
            get
            {
                return this._thisField;
            }
            set
            {
                this._thisField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string locale
        {
            get
            {
                return this.localeField;
            }
            set
            {
                this.localeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class RetrieveHaBackupConfigurationRequestType
    {
        
        private ManagedObjectReference _thisField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ManagedObjectReference _this
        {
            get
            {
                return this._thisField;
            }
            set
            {
                this._thisField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class DynamicProperty
    {
        
        private string nameField;
        
        private object valField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public object val
        {
            get
            {
                return this.valField;
            }
            set
            {
                this.valField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class KeyAnyValue : DynamicData
    {
        
        private string keyField;
        
        private object valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string key
        {
            get
            {
                return this.keyField;
            }
            set
            {
                this.keyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public object value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LocalizableMessage : DynamicData
    {
        
        private string keyField;
        
        private KeyAnyValue[] argField;
        
        private string messageField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string key
        {
            get
            {
                return this.keyField;
            }
            set
            {
                this.keyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("arg", Order=1)]
        public KeyAnyValue[] arg
        {
            get
            {
                return this.argField;
            }
            set
            {
                this.argField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string message
        {
            get
            {
                return this.messageField;
            }
            set
            {
                this.messageField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupServiceContent : DynamicData
    {
        
        private ManagedObjectReference lookupServiceField;
        
        private ManagedObjectReference serviceRegistrationField;
        
        private ManagedObjectReference deploymentInformationServiceField;
        
        private ManagedObjectReference l10nField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ManagedObjectReference lookupService
        {
            get
            {
                return this.lookupServiceField;
            }
            set
            {
                this.lookupServiceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public ManagedObjectReference serviceRegistration
        {
            get
            {
                return this.serviceRegistrationField;
            }
            set
            {
                this.serviceRegistrationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public ManagedObjectReference deploymentInformationService
        {
            get
            {
                return this.deploymentInformationServiceField;
            }
            set
            {
                this.deploymentInformationServiceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public ManagedObjectReference l10n
        {
            get
            {
                return this.l10nField;
            }
            set
            {
                this.l10nField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupHaBackupNodeConfiguration : DynamicData
    {
        
        private string dbTypeField;
        
        private string dbJdbcUrlField;
        
        private string dbUserField;
        
        private string dbPassField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string dbType
        {
            get
            {
                return this.dbTypeField;
            }
            set
            {
                this.dbTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string dbJdbcUrl
        {
            get
            {
                return this.dbJdbcUrlField;
            }
            set
            {
                this.dbJdbcUrlField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string dbUser
        {
            get
            {
                return this.dbUserField;
            }
            set
            {
                this.dbUserField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string dbPass
        {
            get
            {
                return this.dbPassField;
            }
            set
            {
                this.dbPassField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupFaultUnsupportedSiteFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupFaultEntryNotFoundFault))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LookupFaultEntryExistsFault))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupFaultServiceFault : MethodFault
    {
        
        private string errorMessageField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string errorMessage
        {
            get
            {
                return this.errorMessageField;
            }
            set
            {
                this.errorMessageField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupFaultUnsupportedSiteFault : LookupFaultServiceFault
    {
        
        private string operatingSiteField;
        
        private string requestedSiteField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string operatingSite
        {
            get
            {
                return this.operatingSiteField;
            }
            set
            {
                this.operatingSiteField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string requestedSite
        {
            get
            {
                return this.requestedSiteField;
            }
            set
            {
                this.requestedSiteField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupFaultEntryNotFoundFault : LookupFaultServiceFault
    {
        
        private string nameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class LookupFaultEntryExistsFault : LookupFaultServiceFault
    {
        
        private string nameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class UnexpectedFault : RuntimeFault
    {
        
        private string faultNameField;
        
        private LocalizedMethodFault faultField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string faultName
        {
            get
            {
                return this.faultNameField;
            }
            set
            {
                this.faultNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public LocalizedMethodFault fault
        {
            get
            {
                return this.faultField;
            }
            set
            {
                this.faultField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class SystemError : RuntimeFault
    {
        
        private string reasonField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string reason
        {
            get
            {
                return this.reasonField;
            }
            set
            {
                this.reasonField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class SecurityError : RuntimeFault
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class RequestCanceled : RuntimeFault
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class NotSupported : RuntimeFault
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class NotImplemented : RuntimeFault
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class NotEnoughLicenses : RuntimeFault
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class ManagedObjectNotFound : RuntimeFault
    {
        
        private ManagedObjectReference objField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ManagedObjectReference obj
        {
            get
            {
                return this.objField;
            }
            set
            {
                this.objField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MethodNotFound))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidType))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class InvalidRequest : RuntimeFault
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class MethodNotFound : InvalidRequest
    {
        
        private ManagedObjectReference receiverField;
        
        private string methodField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ManagedObjectReference receiver
        {
            get
            {
                return this.receiverField;
            }
            set
            {
                this.receiverField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string method
        {
            get
            {
                return this.methodField;
            }
            set
            {
                this.methodField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class InvalidType : InvalidRequest
    {
        
        private string argumentField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string argument
        {
            get
            {
                return this.argumentField;
            }
            set
            {
                this.argumentField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class InvalidArgument : RuntimeFault
    {
        
        private string invalidPropertyField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string invalidProperty
        {
            get
            {
                return this.invalidPropertyField;
            }
            set
            {
                this.invalidPropertyField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(HostNotReachable))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(HostNotConnected))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class HostCommunication : RuntimeFault
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class HostNotReachable : HostCommunication
    {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="urn:lookup")]
    public partial class HostNotConnected : HostCommunication
    {
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="urn:lookup", ConfigurationName="LookupServiceReference.LsPortType")]
    public interface LsPortType
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:lookup/3.0", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.RuntimeFault), Action="urn:lookup/3.0", Name="RuntimeFaultFault")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetSiteIdRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ListRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetTrustAnchorRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DeleteRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(CreateRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveServiceContentRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveHaBackupConfigurationRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicProperty[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(KeyAnyValue[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LocalizableMessage[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationInfo[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationEndpoint[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationAttribute[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(string[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(object[]))]
        [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
        System.Threading.Tasks.Task<LookupServiceReference.LookupHaBackupNodeConfiguration> RetrieveHaBackupConfigurationAsync(LookupServiceReference.ManagedObjectReference _this);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:lookup/3.0", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.RuntimeFault), Action="urn:lookup/3.0", Name="RuntimeFaultFault")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetSiteIdRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ListRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetTrustAnchorRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DeleteRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(CreateRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveServiceContentRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveHaBackupConfigurationRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicProperty[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(KeyAnyValue[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LocalizableMessage[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationInfo[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationEndpoint[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationAttribute[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(string[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(object[]))]
        [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
        System.Threading.Tasks.Task<string> SetLocaleAsync(LookupServiceReference.ManagedObjectReference _this, string locale);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:lookup/3.0", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.RuntimeFault), Action="urn:lookup/3.0", Name="RuntimeFaultFault")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetSiteIdRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ListRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetTrustAnchorRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DeleteRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(CreateRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveServiceContentRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveHaBackupConfigurationRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicProperty[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(KeyAnyValue[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LocalizableMessage[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationInfo[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationEndpoint[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationAttribute[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(string[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(object[]))]
        [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
        System.Threading.Tasks.Task<string> GetLocaleAsync(LookupServiceReference.ManagedObjectReference _this);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:lookup/3.0", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.RuntimeFault), Action="urn:lookup/3.0", Name="RuntimeFaultFault")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetSiteIdRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ListRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetTrustAnchorRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DeleteRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(CreateRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveServiceContentRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveHaBackupConfigurationRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicProperty[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(KeyAnyValue[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LocalizableMessage[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationInfo[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationEndpoint[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationAttribute[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(string[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(object[]))]
        [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
        System.Threading.Tasks.Task<LookupServiceReference.LookupServiceContent> RetrieveServiceContentAsync(LookupServiceReference.ManagedObjectReference _this);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:lookup/3.0", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.LookupFaultEntryExistsFault), Action="urn:lookup/3.0", Name="LookupFaultEntryExistsFaultFault")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.InvalidArgument), Action="urn:lookup/3.0", Name="InvalidArgumentFault")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.SecurityError), Action="urn:lookup/3.0", Name="SecurityErrorFault")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.RuntimeFault), Action="urn:lookup/3.0", Name="RuntimeFaultFault")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetSiteIdRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ListRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetTrustAnchorRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DeleteRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(CreateRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveServiceContentRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveHaBackupConfigurationRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicProperty[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(KeyAnyValue[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LocalizableMessage[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationInfo[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationEndpoint[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationAttribute[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(string[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(object[]))]
        System.Threading.Tasks.Task CreateAsync(LookupServiceReference.ManagedObjectReference _this, string serviceId, LookupServiceReference.LookupServiceRegistrationCreateSpec createSpec);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:lookup/3.0", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.LookupFaultEntryNotFoundFault), Action="urn:lookup/3.0", Name="LookupFaultEntryNotFoundFaultFault")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.SecurityError), Action="urn:lookup/3.0", Name="SecurityErrorFault")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.RuntimeFault), Action="urn:lookup/3.0", Name="RuntimeFaultFault")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetSiteIdRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ListRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetTrustAnchorRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DeleteRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(CreateRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveServiceContentRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveHaBackupConfigurationRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicProperty[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(KeyAnyValue[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LocalizableMessage[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationInfo[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationEndpoint[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationAttribute[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(string[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(object[]))]
        System.Threading.Tasks.Task DeleteAsync(LookupServiceReference.ManagedObjectReference _this, string serviceId);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:lookup/3.0", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.LookupFaultEntryNotFoundFault), Action="urn:lookup/3.0", Name="LookupFaultEntryNotFoundFaultFault")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.InvalidArgument), Action="urn:lookup/3.0", Name="InvalidArgumentFault")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.SecurityError), Action="urn:lookup/3.0", Name="SecurityErrorFault")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.RuntimeFault), Action="urn:lookup/3.0", Name="RuntimeFaultFault")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetSiteIdRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ListRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetTrustAnchorRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DeleteRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(CreateRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveServiceContentRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveHaBackupConfigurationRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicProperty[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(KeyAnyValue[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LocalizableMessage[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationInfo[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationEndpoint[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationAttribute[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(string[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(object[]))]
        System.Threading.Tasks.Task SetAsync(LookupServiceReference.ManagedObjectReference _this, string serviceId, LookupServiceReference.LookupServiceRegistrationSetSpec serviceSpec);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:lookup/3.0", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.InvalidArgument), Action="urn:lookup/3.0", Name="InvalidArgumentFault")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.SecurityError), Action="urn:lookup/3.0", Name="SecurityErrorFault")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.RuntimeFault), Action="urn:lookup/3.0", Name="RuntimeFaultFault")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetSiteIdRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ListRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetTrustAnchorRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DeleteRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(CreateRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveServiceContentRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveHaBackupConfigurationRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicProperty[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(KeyAnyValue[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LocalizableMessage[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationInfo[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationEndpoint[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationAttribute[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(string[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(object[]))]
        System.Threading.Tasks.Task<LookupServiceReference.SetTrustAnchorResponse> SetTrustAnchorAsync(LookupServiceReference.SetTrustAnchorRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:lookup/3.0", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.LookupFaultEntryNotFoundFault), Action="urn:lookup/3.0", Name="LookupFaultEntryNotFoundFaultFault")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.RuntimeFault), Action="urn:lookup/3.0", Name="RuntimeFaultFault")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetSiteIdRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ListRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetTrustAnchorRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DeleteRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(CreateRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveServiceContentRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveHaBackupConfigurationRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicProperty[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(KeyAnyValue[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LocalizableMessage[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationInfo[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationEndpoint[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationAttribute[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(string[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(object[]))]
        [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
        System.Threading.Tasks.Task<LookupServiceReference.LookupServiceRegistrationInfo> GetAsync(LookupServiceReference.ManagedObjectReference _this, string serviceId);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:lookup/3.0", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.RuntimeFault), Action="urn:lookup/3.0", Name="RuntimeFaultFault")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetSiteIdRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ListRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetTrustAnchorRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DeleteRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(CreateRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveServiceContentRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveHaBackupConfigurationRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicProperty[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(KeyAnyValue[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LocalizableMessage[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationInfo[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationEndpoint[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationAttribute[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(string[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(object[]))]
        System.Threading.Tasks.Task<LookupServiceReference.ListResponse> ListAsync(LookupServiceReference.ListRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:lookup/3.0", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(LookupServiceReference.RuntimeFault), Action="urn:lookup/3.0", Name="RuntimeFaultFault")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetSiteIdRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(ListRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetTrustAnchorRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DeleteRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(CreateRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveServiceContentRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(GetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(SetLocaleRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(RetrieveHaBackupConfigurationRequestType))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicData))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(DynamicProperty[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(KeyAnyValue[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LocalizableMessage[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationInfo[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationEndpoint[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(LookupServiceRegistrationAttribute[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(string[]))]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(object[]))]
        [return: System.ServiceModel.MessageParameterAttribute(Name="returnval")]
        System.Threading.Tasks.Task<string> GetSiteIdAsync(LookupServiceReference.ManagedObjectReference _this);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetTrustAnchor", WrapperNamespace="urn:lookup", IsWrapped=true)]
    public partial class SetTrustAnchorRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:lookup", Order=0)]
        public LookupServiceReference.ManagedObjectReference _this;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:lookup", Order=1)]
        public LookupServiceReference.LookupServiceRegistrationFilter filter;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:lookup", Order=2)]
        [System.Xml.Serialization.XmlElementAttribute("trustAnchors")]
        public string[] trustAnchors;
        
        public SetTrustAnchorRequest()
        {
        }
        
        public SetTrustAnchorRequest(LookupServiceReference.ManagedObjectReference _this, LookupServiceReference.LookupServiceRegistrationFilter filter, string[] trustAnchors)
        {
            this._this = _this;
            this.filter = filter;
            this.trustAnchors = trustAnchors;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetTrustAnchorResponse", WrapperNamespace="urn:lookup", IsWrapped=true)]
    public partial class SetTrustAnchorResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:lookup", Order=0)]
        public int returnval;
        
        public SetTrustAnchorResponse()
        {
        }
        
        public SetTrustAnchorResponse(int returnval)
        {
            this.returnval = returnval;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="List", WrapperNamespace="urn:lookup", IsWrapped=true)]
    public partial class ListRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:lookup", Order=0)]
        public LookupServiceReference.ManagedObjectReference _this;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:lookup", Order=1)]
        public LookupServiceReference.LookupServiceRegistrationFilter filterCriteria;
        
        public ListRequest()
        {
        }
        
        public ListRequest(LookupServiceReference.ManagedObjectReference _this, LookupServiceReference.LookupServiceRegistrationFilter filterCriteria)
        {
            this._this = _this;
            this.filterCriteria = filterCriteria;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="ListResponse", WrapperNamespace="urn:lookup", IsWrapped=true)]
    public partial class ListResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="urn:lookup", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute("returnval")]
        public LookupServiceReference.LookupServiceRegistrationInfo[] returnval;
        
        public ListResponse()
        {
        }
        
        public ListResponse(LookupServiceReference.LookupServiceRegistrationInfo[] returnval)
        {
            this.returnval = returnval;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    public interface LsPortTypeChannel : LookupServiceReference.LsPortType, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil", "*******")]
    public partial class LsPortTypeClient : System.ServiceModel.ClientBase<LookupServiceReference.LsPortType>, LookupServiceReference.LsPortType
    {
        
        public LsPortTypeClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public System.Threading.Tasks.Task<LookupServiceReference.LookupHaBackupNodeConfiguration> RetrieveHaBackupConfigurationAsync(LookupServiceReference.ManagedObjectReference _this)
        {
            return base.Channel.RetrieveHaBackupConfigurationAsync(_this);
        }
        
        public System.Threading.Tasks.Task<string> SetLocaleAsync(LookupServiceReference.ManagedObjectReference _this, string locale)
        {
            return base.Channel.SetLocaleAsync(_this, locale);
        }
        
        public System.Threading.Tasks.Task<string> GetLocaleAsync(LookupServiceReference.ManagedObjectReference _this)
        {
            return base.Channel.GetLocaleAsync(_this);
        }
        
        public System.Threading.Tasks.Task<LookupServiceReference.LookupServiceContent> RetrieveServiceContentAsync(LookupServiceReference.ManagedObjectReference _this)
        {
            return base.Channel.RetrieveServiceContentAsync(_this);
        }
        
        public System.Threading.Tasks.Task CreateAsync(LookupServiceReference.ManagedObjectReference _this, string serviceId, LookupServiceReference.LookupServiceRegistrationCreateSpec createSpec)
        {
            return base.Channel.CreateAsync(_this, serviceId, createSpec);
        }
        
        public System.Threading.Tasks.Task DeleteAsync(LookupServiceReference.ManagedObjectReference _this, string serviceId)
        {
            return base.Channel.DeleteAsync(_this, serviceId);
        }
        
        public System.Threading.Tasks.Task SetAsync(LookupServiceReference.ManagedObjectReference _this, string serviceId, LookupServiceReference.LookupServiceRegistrationSetSpec serviceSpec)
        {
            return base.Channel.SetAsync(_this, serviceId, serviceSpec);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<LookupServiceReference.SetTrustAnchorResponse> LookupServiceReference.LsPortType.SetTrustAnchorAsync(LookupServiceReference.SetTrustAnchorRequest request)
        {
            return base.Channel.SetTrustAnchorAsync(request);
        }
        
        public System.Threading.Tasks.Task<LookupServiceReference.SetTrustAnchorResponse> SetTrustAnchorAsync(LookupServiceReference.ManagedObjectReference _this, LookupServiceReference.LookupServiceRegistrationFilter filter, string[] trustAnchors)
        {
            LookupServiceReference.SetTrustAnchorRequest inValue = new LookupServiceReference.SetTrustAnchorRequest();
            inValue._this = _this;
            inValue.filter = filter;
            inValue.trustAnchors = trustAnchors;
            return ((LookupServiceReference.LsPortType)(this)).SetTrustAnchorAsync(inValue);
        }
        
        public System.Threading.Tasks.Task<LookupServiceReference.LookupServiceRegistrationInfo> GetAsync(LookupServiceReference.ManagedObjectReference _this, string serviceId)
        {
            return base.Channel.GetAsync(_this, serviceId);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<LookupServiceReference.ListResponse> LookupServiceReference.LsPortType.ListAsync(LookupServiceReference.ListRequest request)
        {
            return base.Channel.ListAsync(request);
        }
        
        public System.Threading.Tasks.Task<LookupServiceReference.ListResponse> ListAsync(LookupServiceReference.ManagedObjectReference _this, LookupServiceReference.LookupServiceRegistrationFilter filterCriteria)
        {
            LookupServiceReference.ListRequest inValue = new LookupServiceReference.ListRequest();
            inValue._this = _this;
            inValue.filterCriteria = filterCriteria;
            return ((LookupServiceReference.LsPortType)(this)).ListAsync(inValue);
        }
        
        public System.Threading.Tasks.Task<string> GetSiteIdAsync(LookupServiceReference.ManagedObjectReference _this)
        {
            return base.Channel.GetSiteIdAsync(_this);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
    }
}
