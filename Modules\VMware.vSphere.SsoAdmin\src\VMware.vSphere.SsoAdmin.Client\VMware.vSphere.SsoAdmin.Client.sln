﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.30503.244
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VMware.vSphere.SsoAdminClient", "VMware.vSphere.SsoAdminClient\VMware.vSphere.SsoAdminClient.csproj", "{BD48E0DD-4048-48FD-B0BE-560E2417A2CC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VMware.vSphere.LsClient", "VMware.vSphere.LsClient\VMware.vSphere.LsClient.csproj", "{EEC4C335-3E6C-4FA5-84CD-CBADCD720F35}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VMware.vSphere.SsoAdmin.Utils", "VMware.vSphere.SsoAdmin.Utils\VMware.vSphere.SsoAdmin.Utils.csproj", "{1523743E-C01E-4D37-845F-0BB8DAF9EE7E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VMware.vSphere.SsoAdminClient.Tests", "VMware.vSphere.SsoAdminClient.Tests\VMware.vSphere.SsoAdminClient.Tests.csproj", "{90E6C4A6-FDB4-43FC-B156-ADBCF2B85CCE}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{BD48E0DD-4048-48FD-B0BE-560E2417A2CC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BD48E0DD-4048-48FD-B0BE-560E2417A2CC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BD48E0DD-4048-48FD-B0BE-560E2417A2CC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BD48E0DD-4048-48FD-B0BE-560E2417A2CC}.Release|Any CPU.Build.0 = Release|Any CPU
		{EEC4C335-3E6C-4FA5-84CD-CBADCD720F35}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EEC4C335-3E6C-4FA5-84CD-CBADCD720F35}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EEC4C335-3E6C-4FA5-84CD-CBADCD720F35}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EEC4C335-3E6C-4FA5-84CD-CBADCD720F35}.Release|Any CPU.Build.0 = Release|Any CPU
		{1523743E-C01E-4D37-845F-0BB8DAF9EE7E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1523743E-C01E-4D37-845F-0BB8DAF9EE7E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1523743E-C01E-4D37-845F-0BB8DAF9EE7E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1523743E-C01E-4D37-845F-0BB8DAF9EE7E}.Release|Any CPU.Build.0 = Release|Any CPU
		{90E6C4A6-FDB4-43FC-B156-ADBCF2B85CCE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{90E6C4A6-FDB4-43FC-B156-ADBCF2B85CCE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{90E6C4A6-FDB4-43FC-B156-ADBCF2B85CCE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{90E6C4A6-FDB4-43FC-B156-ADBCF2B85CCE}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {9A376526-4487-43FF-A527-E34AD4764F12}
	EndGlobalSection
EndGlobal
