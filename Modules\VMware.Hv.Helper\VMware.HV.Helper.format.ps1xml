<?xml version="1.0" encoding="utf-8" ?>
      <Configuration>
          <ViewDefinitions>
              <View>
                  <Name>VMware.HV.DesktopSummaryView</Name>
                  <ViewSelectedBy>
                      <TypeName>VMware.HV.DesktopSummaryView</TypeName>
                  </ViewSelectedBy>
                  <TableControl>
                      <TableHeaders>
                          <TableColumnHeader>
                              <Width>12</Width>
							  <Label>Name</Label>
                          </TableColumnHeader>
                          <TableColumnHeader>
                              <Width>16</Width>
							  <Label>DisplayName</Label>
                          </TableColumnHeader>
                          <TableColumnHeader>
                              <Width>12</Width>
							  <Label>Type</Label>
                          </TableColumnHeader>
                           <TableColumnHeader>
                              <Width>18</Width>
							  <Label>Source</Label>
                          </TableColumnHeader>
						  <TableColumnHeader>
                              <Width>16</Width>
							  <Label>User Assignment</Label>
                          </TableColumnHeader>
						  <TableColumnHeader>
                              <Width>8</Width>
							  <Label>Entitled</Label>
                          </TableColumnHeader>
						  <TableColumnHeader>
                              <Width>7</Width>
							  <Label>Enabled</Label>
                          </TableColumnHeader>
						  <TableColumnHeader>
                              <Width>10</Width>
							  <Label>Sessions</Label>
							  <Alignment>Right</Alignment>
                          </TableColumnHeader>
                      </TableHeaders>
                      <TableRowEntries>
                          <TableRowEntry>
                              <TableColumnItems>
                                  <TableColumnItem>
                                      <ScriptBlock>$_.desktopSummaryData.name</ScriptBlock>
                                  </TableColumnItem>
                                  <TableColumnItem>
                                      <ScriptBlock>$_.desktopSummaryData.displayName</ScriptBlock>
                                  </TableColumnItem>
                                  <TableColumnItem>
                                      <ScriptBlock>$_.desktopSummaryData.type</ScriptBlock>
                                  </TableColumnItem>
                                  <TableColumnItem>
                                      <ScriptBlock>$_.desktopSummaryData.source</ScriptBlock>
                                  </TableColumnItem>
								  <TableColumnItem>
                                      <ScriptBlock>$_.desktopSummaryData.userAssignment</ScriptBlock>
                                  </TableColumnItem>
								  <TableColumnItem>
                                      <ScriptBlock>
									    $filterContains = Get-HVQueryFilter localData.desktops -contains ([VMware.Hv.DesktopId[]]$_.id)
                                        $GlobalfilterContains = Get-HVQueryFilter localData.desktops -contains ([VMware.Hv.DesktopId[]]$_.id)
                                        Try {   
                                          $results += Get-HVQueryResult -EntityType EntitledUserOrGroupLocalSummaryView -Filter $filterContains
                                          $results += Get-HVQueryResult -EntityType EntitledUserOrGroupGlobalSummaryView -Filter $GlobalfilterContains
                                        } Catch {
                                          #Do nothing  
                                        }
                                        $results.length
									  </ScriptBlock>
                                  </TableColumnItem>
								  <TableColumnItem>
                                      <ScriptBlock>$_.desktopSummaryData.enabled</ScriptBlock>
                                  </TableColumnItem>
								  <TableColumnItem>
                                      <ScriptBlock>$_.desktopSummaryData.numSessions</ScriptBlock>
                                  </TableColumnItem>
                              </TableColumnItems>
                          </TableRowEntry>
                       </TableRowEntries>
                  </TableControl>
              </View>
			  <View>
			  <Name>VMware.HV.DesktopSummaryViewList</Name>
                  <ViewSelectedBy>
                      <TypeName>VMware.HV.DesktopSummaryView</TypeName>
                  </ViewSelectedBy>
					<ListControl>
					<ListEntries>
					  <ListEntry>
					    <ListItems>
						  <ListItem>
						  <Label>Name</Label>
						  <ScriptBlock>$_.desktopSummaryData.name</ScriptBlock>
						  </ListItem>
						  <ListItem>
						  <Label>DisplayName</Label>
						  <ScriptBlock>$_.desktopSummaryData.displayName</ScriptBlock>
						  </ListItem>
						  <ListItem>
						  <Label>Type</Label>
						  <ScriptBlock>$_.desktopSummaryData.type</ScriptBlock>
						  </ListItem>
						  <ListItem>
						  <Label>Source</Label>
						  <ScriptBlock>$_.desktopSummaryData.source</ScriptBlock>
						  </ListItem>
						  <ListItem>
						  <Label>User Assignment</Label>
						  <ScriptBlock>$_.desktopSummaryData.userAssignment</ScriptBlock>
						  </ListItem>
						  <ListItem>
						  <Label>Entitled</Label>
						  <ScriptBlock>
						    $filterContains = Get-HVQueryFilter localData.desktops -contains ([VMware.Hv.DesktopId[]]$_.id)
                            $GlobalfilterContains = Get-HVQueryFilter localData.desktops -contains ([VMware.Hv.DesktopId[]]$_.id)
                            Try {   
                              $results += Get-HVQueryResult -EntityType EntitledUserOrGroupLocalSummaryView -Filter $filterContains
                              $results += Get-HVQueryResult -EntityType EntitledUserOrGroupGlobalSummaryView -Filter $GlobalfilterContains
                            } Catch {
                              #Do nothing  
                            }
                            $results.length
						  </ScriptBlock>
						  </ListItem>
						  <ListItem>
						  <Label>Enabled</Label>
						  <ScriptBlock>$_.desktopSummaryData.enabled</ScriptBlock>
						  </ListItem>
						  <ListItem>
						  <Label>Sessions</Label>
						  <ScriptBlock>$_.desktopSummaryData.numSessions</ScriptBlock>
						  </ListItem>
						</ListItems>
					  </ListEntry>
					</ListEntries>
				  </ListControl>
            </View>
            <View>
                  <Name>VMware.HV.MachineNamesView</Name>
                  <ViewSelectedBy>
                      <TypeName>VMware.HV.MachineNamesView</TypeName>
                  </ViewSelectedBy>
                  <TableControl>
                      <TableHeaders>
                          <TableColumnHeader>
                              <Width>15</Width>
							  <Label>Machine</Label>
                          </TableColumnHeader>
                          <TableColumnHeader>
                              <Width>12</Width>
							  <Label>DesktopPool</Label>
                          </TableColumnHeader>
                          <TableColumnHeader>
                              <Width>12</Width>
							  <Label>DNS Name</Label>
                          </TableColumnHeader>
                           <TableColumnHeader>
                              <Width>8</Width>
							  <Label>User</Label>
                          </TableColumnHeader>
						  <TableColumnHeader>
                              <Width>15</Width>
							  <Label>Host</Label>
                          </TableColumnHeader>
						  <TableColumnHeader>
                              <Width>5</Width>
							  <Label>Agent</Label>
                          </TableColumnHeader>
						  <TableColumnHeader>
                              <Width>10</Width>
							  <Label>Datastore</Label>
                          </TableColumnHeader>
                          <TableColumnHeader>
                              <Width>15</Width>
							  <Label>Status</Label>
                          </TableColumnHeader>
                      </TableHeaders>
                      <TableRowEntries>
                          <TableRowEntry>
                              <TableColumnItems>
                                  <TableColumnItem>
                                      <ScriptBlock>$_.Base.Name</ScriptBlock>
                                  </TableColumnItem>
                                  <TableColumnItem>
                                      <ScriptBlock>$_.NamesData.desktopName</ScriptBlock>
                                  </TableColumnItem>
                                  <TableColumnItem>
                                      <ScriptBlock>$_.Base.DnsName</ScriptBlock>
                                  </TableColumnItem>
                                  <TableColumnItem>
                                      <ScriptBlock>$_.NamesData.UserName</ScriptBlock>
                                  </TableColumnItem>
								  <TableColumnItem>
                                      <ScriptBlock>$_.ManagedMachineNamesData.HostName</ScriptBlock>
                                  </TableColumnItem>
								  <TableColumnItem>
                                      <ScriptBlock>$_.Base.AgentVersion</ScriptBlock>
                                  </TableColumnItem>
                                  <TableColumnItem>
                                      <ScriptBlock>$_.ManagedMachineNamesData.DatastorePaths</ScriptBlock>
                                  </TableColumnItem>
								  <TableColumnItem>
                                      <ScriptBlock>$_.Base.BasicState</ScriptBlock>
                                  </TableColumnItem>
                              </TableColumnItems>
                          </TableRowEntry>
                       </TableRowEntries>
                  </TableControl>
              </View>
			  <View>
			  <Name>VMware.HV.MachineNamesViewList</Name>
                  <ViewSelectedBy>
                      <TypeName>VMware.HV.MachineNamesView</TypeName>
                  </ViewSelectedBy>
					<ListControl>
					<ListEntries>
					  <ListEntry>
					    <ListItems>
						  <ListItem>
						  <Label>Name</Label>
						  <ScriptBlock>$_.Base.Name</ScriptBlock>
						  </ListItem>
						  <ListItem>
						  <Label>DisplayName</Label>
						  <ScriptBlock>$_.NamesData.desktopName</ScriptBlock>
						  </ListItem>
						  <ListItem>
						  <Label>Type</Label>
						  <ScriptBlock>$_.Base.DnsName</ScriptBlock>
						  </ListItem>
						  <ListItem>
						  <Label>Source</Label>
						  <ScriptBlock>$_.NamesData.UserName</ScriptBlock>
						  </ListItem>
						  <ListItem>
						  <Label>User Assignment</Label>
						  <ScriptBlock>$_.ManagedMachineNamesData.HostName</ScriptBlock>
						  </ListItem>
						  <ListItem>
                          <Label>Agent</Label>
						  <ScriptBlock>$_.Base.AgentVersion</ScriptBlock>
						  </ListItem>
						  <ListItem>
						  <Label>Datastore</Label>
						  <ScriptBlock>$_.ManagedMachineNamesData.DatastorePaths</ScriptBlock>
						  </ListItem>
						  <ListItem>
						  <Label>Status</Label>
						  <ScriptBlock>$_.Base.BasicState</ScriptBlock>
						  </ListItem>
						</ListItems>
					  </ListEntry>
					</ListEntries>
				  </ListControl>
            </View>
          </ViewDefinitions>
      </Configuration>