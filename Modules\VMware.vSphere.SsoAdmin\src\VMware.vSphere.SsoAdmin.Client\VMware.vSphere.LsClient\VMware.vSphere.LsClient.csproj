<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <RootNamespace>VMware.vSphere.LsClient</RootNamespace>
    <AssemblyName>VMware.vSphere.LsClient</AssemblyName>
    <Description>vSphere Lookup Service API client.</Description>
    <TargetFrameworks>net45;netcoreapp3.1</TargetFrameworks>
  </PropertyGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net45'">
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.ServiceModel" />
  </ItemGroup>
  
  <ItemGroup Condition="'$(TargetFramework)' == 'netcoreapp3.1'">
    <PackageReference Include="VMware.System.Private.ServiceModel" Version="4.4.4" />
  </ItemGroup>

  <ItemGroup>
    <WCFMetadata Include="Connected Services" />
  </ItemGroup>

</Project>