﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <RootNamespace>VMware.vSphere.SsoAdminClient</RootNamespace>
    <AssemblyName>VMware.vSphere.SsoAdminClient</AssemblyName>
    <Description>SSO Admin API client.</Description>
    <TargetFrameworks>net45;netcoreapp3.1</TargetFrameworks>
  </PropertyGroup>
  
  <PropertyGroup Condition=" '$(TargetFramework)' == 'net45' ">
    <DefineConstants>$(DefineConstants);NET45</DefineConstants>
  </PropertyGroup>
  
  <PropertyGroup Condition=" '$(TargetFramework)' == 'netcoreapp3.1' ">
    <DefineConstants>$(DefineConstants);NETCORE20</DefineConstants>
  </PropertyGroup>
  
  <ItemGroup Condition="'$(TargetFramework)' == 'net45'">
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.ServiceModel" />
  </ItemGroup>
  
  <ItemGroup Condition="'$(TargetFramework)' == 'netcoreapp3.1'">
    <PackageReference Include="VMware.System.Private.ServiceModel" Version="4.4.4" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="VMware.Binding.Sts" Version="12.0.0.15939652" />
    <PackageReference Include="VMware.Binding.WsTrust" Version="12.0.0.15939652" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\VMware.vSphere.LsClient\VMware.vSphere.LsClient.csproj" />
  </ItemGroup>

</Project>
